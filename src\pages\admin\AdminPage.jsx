import React, { useState, useRef } from 'react'
import {
  Container,
  Typo<PERSON>,
  Box,
  Grid,
  Card,
  CardContent,
  TextField,
  Button,
  Tabs,
  Tab,
  Paper,
  Alert,
  CircularProgress,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip
} from '@mui/material'
import {
  Palette,
  Inventory,
  Upload,
  Save,
  Refresh,
  Preview,
  Delete,
  CloudUpload
} from '@mui/icons-material'
import { useTranslation } from 'react-i18next'
import { motion } from 'framer-motion'
import Papa from 'papaparse'

// Theme and hooks
import { createDynamicTheme, saveThemeConfig, resetThemeConfig } from '../../theme/theme'
import { useNotification } from '../../hooks/useNotification'

const AdminPage = () => {
  const { t } = useTranslation()
  const { showSuccess, showError, showInfo } = useNotification()
  const fileInputRef = useRef(null)

  const [activeTab, setActiveTab] = useState(0)
  const [loading, setLoading] = useState(false)

  // Brand Configuration State
  const [brandConfig, setBrandConfig] = useState(() => {
    try {
      const saved = localStorage.getItem('jaleelThemeConfig')
      return saved ? JSON.parse(saved) : {
        primaryColor: '#1976d2',
        secondaryColor: '#ff9800',
        logoUrl: 'https://jaleelcashandcarry.com/assets/img/logo.png',
        fontFamily: 'Roboto'
      }
    } catch {
      return {
        primaryColor: '#1976d2',
        secondaryColor: '#ff9800',
        logoUrl: 'https://jaleelcashandcarry.com/assets/img/logo.png',
        fontFamily: 'Roboto'
      }
    }
  })

  // Inventory State
  const [uploadedProducts, setUploadedProducts] = useState(() => {
    try {
      const saved = localStorage.getItem('jaleelUploadedProducts')
      return saved ? JSON.parse(saved) : []
    } catch {
      return []
    }
  })

  const fontOptions = [
    'Roboto',
    'Arial',
    'Helvetica',
    'Georgia',
    'Times New Roman',
    'Verdana',
    'Tahoma',
    'Open Sans',
    'Lato',
    'Montserrat'
  ]

  const handleBrandConfigChange = (field, value) => {
    const newConfig = { ...brandConfig, [field]: value }
    setBrandConfig(newConfig)

    // Apply changes immediately for preview
    saveThemeConfig(newConfig)

    // Trigger theme update event
    window.dispatchEvent(new Event('themeConfigChanged'))

    showInfo('Preview updated - Save to persist changes')
  }

  const handleSaveBrandConfig = () => {
    setLoading(true)
    setTimeout(() => {
      saveThemeConfig(brandConfig)
      window.dispatchEvent(new Event('themeConfigChanged'))
      showSuccess(t('admin.configSaved'))
      setLoading(false)
    }, 1000)
  }

  const handleResetBrandConfig = () => {
    setLoading(true)
    setTimeout(() => {
      resetThemeConfig()
      setBrandConfig({
        primaryColor: '#1976d2',
        secondaryColor: '#ff9800',
        logoUrl: 'https://jaleelcashandcarry.com/assets/img/logo.png',
        fontFamily: 'Roboto'
      })
      window.dispatchEvent(new Event('themeConfigChanged'))
      showSuccess(t('admin.configReset'))
      setLoading(false)
    }, 1000)
  }

  const handleFileUpload = (event) => {
    const file = event.target.files[0]
    if (!file) return

    if (file.type !== 'text/csv') {
      showError('Please upload a CSV file')
      return
    }

    setLoading(true)

    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: (results) => {
        try {
          const products = results.data.map((row, index) => ({
            id: `uploaded-${Date.now()}-${index}`,
            name: row.name || row.Name || '',
            price: parseFloat(row.price || row.Price || 0),
            imageUrl: row.imageUrl || row.ImageUrl || row.image || row.Image || '',
            stock: parseInt(row.stock || row.Stock || 0),
            description: row.description || row.Description || '',
            category: row.category || row.Category || 'Uploaded Products',
            unit: row.unit || row.Unit || 'per item',
            brand: row.brand || row.Brand || 'Generic',
            origin: row.origin || row.Origin || 'Unknown',
            rating: parseFloat(row.rating || row.Rating || 4.0),
            reviews: parseInt(row.reviews || row.Reviews || 0)
          })).filter(product => product.name && product.price > 0)

          if (products.length === 0) {
            showError(t('admin.invalidCsv'))
            setLoading(false)
            return
          }

          // Save to localStorage
          const allProducts = [...uploadedProducts, ...products]
          setUploadedProducts(allProducts)
          localStorage.setItem('jaleelUploadedProducts', JSON.stringify(allProducts))

          showSuccess(`${t('admin.productsUploaded')} (${products.length} products)`)
          setLoading(false)
        } catch (error) {
          showError(t('admin.invalidCsv'))
          setLoading(false)
        }
      },
      error: (error) => {
        showError('Error parsing CSV file')
        setLoading(false)
      }
    })
  }

  const handleDeleteProduct = (productId) => {
    const updatedProducts = uploadedProducts.filter(p => p.id !== productId)
    setUploadedProducts(updatedProducts)
    localStorage.setItem('jaleelUploadedProducts', JSON.stringify(updatedProducts))
    showInfo('Product deleted')
  }

  const handleClearAllProducts = () => {
    setUploadedProducts([])
    localStorage.removeItem('jaleelUploadedProducts')
    showInfo('All uploaded products cleared')
  }

  const BrandConfigurator = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <Grid container spacing={4}>
        {/* Configuration Form */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                Brand Configuration
              </Typography>

              <Box sx={{ mb: 3 }}>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  {t('admin.primaryColor')}
                </Typography>
                <TextField
                  fullWidth
                  type="color"
                  value={brandConfig.primaryColor}
                  onChange={(e) => handleBrandConfigChange('primaryColor', e.target.value)}
                  sx={{ mb: 2 }}
                />
              </Box>

              <Box sx={{ mb: 3 }}>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  {t('admin.secondaryColor')}
                </Typography>
                <TextField
                  fullWidth
                  type="color"
                  value={brandConfig.secondaryColor}
                  onChange={(e) => handleBrandConfigChange('secondaryColor', e.target.value)}
                  sx={{ mb: 2 }}
                />
              </Box>

              <Box sx={{ mb: 3 }}>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  {t('admin.logoUrl')}
                </Typography>
                <TextField
                  fullWidth
                  value={brandConfig.logoUrl}
                  onChange={(e) => handleBrandConfigChange('logoUrl', e.target.value)}
                  placeholder="https://example.com/logo.png"
                  sx={{ mb: 2 }}
                />
              </Box>

              <Box sx={{ mb: 3 }}>
                <FormControl fullWidth>
                  <InputLabel>{t('admin.fontFamily')}</InputLabel>
                  <Select
                    value={brandConfig.fontFamily}
                    label={t('admin.fontFamily')}
                    onChange={(e) => handleBrandConfigChange('fontFamily', e.target.value)}
                  >
                    {fontOptions.map((font) => (
                      <MenuItem key={font} value={font} sx={{ fontFamily: font }}>
                        {font}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>

              <Box sx={{ display: 'flex', gap: 2 }}>
                <Button
                  variant="contained"
                  onClick={handleSaveBrandConfig}
                  disabled={loading}
                  startIcon={loading ? <CircularProgress size={20} /> : <Save />}
                  sx={{ flex: 1 }}
                >
                  {t('admin.saveChanges')}
                </Button>
                <Button
                  variant="outlined"
                  onClick={handleResetBrandConfig}
                  disabled={loading}
                  startIcon={<Refresh />}
                  sx={{ flex: 1 }}
                >
                  {t('admin.resetToDefault')}
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Live Preview */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                Live Preview
              </Typography>

              <Paper
                sx={{
                  p: 3,
                  bgcolor: brandConfig.primaryColor,
                  color: 'white',
                  mb: 2,
                  fontFamily: brandConfig.fontFamily
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <img
                    src={brandConfig.logoUrl}
                    alt="Logo Preview"
                    style={{
                      height: 40,
                      marginRight: 16,
                      filter: 'brightness(0) invert(1)'
                    }}
                    onError={(e) => {
                      e.target.src = 'https://jaleelcashandcarry.com/assets/img/logo.png'
                    }}
                  />
                  <Typography variant="h6" sx={{ fontFamily: brandConfig.fontFamily }}>
                    Jaleel Cash & Carry
                  </Typography>
                </Box>
                <Typography variant="body2" sx={{ fontFamily: brandConfig.fontFamily }}>
                  Header Preview with Primary Color
                </Typography>
              </Paper>

              <Paper
                sx={{
                  p: 2,
                  border: 2,
                  borderColor: brandConfig.secondaryColor,
                  fontFamily: brandConfig.fontFamily
                }}
              >
                <Typography
                  variant="h6"
                  sx={{
                    color: brandConfig.secondaryColor,
                    fontFamily: brandConfig.fontFamily,
                    mb: 1
                  }}
                >
                  Sample Product Card
                </Typography>
                <Typography variant="body2" sx={{ fontFamily: brandConfig.fontFamily }}>
                  Font: {brandConfig.fontFamily}
                </Typography>
                <Button
                  variant="contained"
                  size="small"
                  sx={{
                    mt: 1,
                    bgcolor: brandConfig.secondaryColor,
                    fontFamily: brandConfig.fontFamily
                  }}
                >
                  Add to Cart
                </Button>
              </Paper>

              <Alert severity="info" sx={{ mt: 2 }}>
                Changes are applied immediately for preview. Click "Save Changes" to persist.
              </Alert>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </motion.div>
  )

  const InventoryManager = () => (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
    >
      <Grid container spacing={4}>
        {/* Upload Section */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                {t('admin.inventoryManagement')}
              </Typography>

              <Alert severity="info" sx={{ mb: 3 }}>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  {t('admin.csvFormat')}
                </Typography>
                <Typography variant="caption">
                  Example: "Fresh Bananas,8.50,https://...,150,Fresh bananas...,Fresh Fruits"
                </Typography>
              </Alert>

              <input
                type="file"
                accept=".csv"
                onChange={handleFileUpload}
                style={{ display: 'none' }}
                ref={fileInputRef}
              />

              <Button
                fullWidth
                variant="contained"
                onClick={() => fileInputRef.current?.click()}
                disabled={loading}
                startIcon={loading ? <CircularProgress size={20} /> : <CloudUpload />}
                sx={{ mb: 2, py: 1.5 }}
              >
                {t('admin.uploadCsv')}
              </Button>

              {uploadedProducts.length > 0 && (
                <Button
                  fullWidth
                  variant="outlined"
                  color="error"
                  onClick={handleClearAllProducts}
                  startIcon={<Delete />}
                >
                  Clear All Products ({uploadedProducts.length})
                </Button>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Products List */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
                Uploaded Products ({uploadedProducts.length})
              </Typography>

              {uploadedProducts.length === 0 ? (
                <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
                  No products uploaded yet. Upload a CSV file to get started.
                </Typography>
              ) : (
                <List sx={{ maxHeight: 400, overflow: 'auto' }}>
                  {uploadedProducts.map((product) => (
                    <ListItem key={product.id} divider>
                      <ListItemText
                        primary={product.name}
                        secondary={
                          <Box>
                            <Typography variant="body2">
                              AED {product.price.toFixed(2)} • {product.category}
                            </Typography>
                            <Box sx={{ mt: 0.5 }}>
                              <Chip
                                label={`Stock: ${product.stock}`}
                                size="small"
                                color={product.stock > 0 ? 'success' : 'error'}
                              />
                            </Box>
                          </Box>
                        }
                      />
                      <ListItemSecondaryAction>
                        <IconButton
                          edge="end"
                          color="error"
                          onClick={() => handleDeleteProduct(product.id)}
                        >
                          <Delete />
                        </IconButton>
                      </ListItemSecondaryAction>
                    </ListItem>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </motion.div>
  )

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" component="h1" sx={{ fontWeight: 600, mb: 4 }}>
        {t('admin.title')}
      </Typography>

      <Paper sx={{ mb: 4 }}>
        <Tabs
          value={activeTab}
          onChange={(e, newValue) => setActiveTab(newValue)}
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab
            icon={<Palette />}
            label={t('admin.brandConfigurator')}
            iconPosition="start"
          />
          <Tab
            icon={<Inventory />}
            label={t('admin.inventoryManagement')}
            iconPosition="start"
          />
        </Tabs>
      </Paper>

      <Box sx={{ mt: 3 }}>
        {activeTab === 0 && <BrandConfigurator />}
        {activeTab === 1 && <InventoryManager />}
      </Box>
    </Container>
  )
}

export default AdminPage
