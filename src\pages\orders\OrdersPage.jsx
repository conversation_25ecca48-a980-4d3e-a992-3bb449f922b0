import React, { useState, useEffect } from 'react'
import {
  Container,
  Typo<PERSON>,
  Box,
  Card,
  CardContent,
  Button,
  Chip,
  Grid,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Divider,
  Paper,
  useTheme,
  useMediaQuery
} from '@mui/material'
import {
  Receipt,
  LocalShipping,
  CheckCircle,
  Cancel,
  Visibility,
  Refresh
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { motion } from 'framer-motion'

// Hooks
import { useAuth } from '../../hooks/useAuth'
import { useNotification } from '../../hooks/useNotification'

const OrdersPage = () => {
  const navigate = useNavigate()
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  const { t } = useTranslation()
  const { user } = useAuth()
  const { showInfo } = useNotification()

  const [orders, setOrders] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadOrders()
  }, [])

  const loadOrders = () => {
    setLoading(true)

    // Simulate API call delay
    setTimeout(() => {
      try {
        const savedOrders = JSON.parse(localStorage.getItem('jaleelOrders') || '[]')
        setOrders(savedOrders)
      } catch (error) {
        console.error('Error loading orders:', error)
        setOrders([])
      } finally {
        setLoading(false)
      }
    }, 500)
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'placed':
        return 'info'
      case 'confirmed':
        return 'primary'
      case 'preparing':
        return 'warning'
      case 'out_for_delivery':
        return 'secondary'
      case 'delivered':
        return 'success'
      case 'cancelled':
        return 'error'
      default:
        return 'default'
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'placed':
        return <Receipt />
      case 'confirmed':
      case 'preparing':
        return <LocalShipping />
      case 'out_for_delivery':
        return <LocalShipping />
      case 'delivered':
        return <CheckCircle />
      case 'cancelled':
        return <Cancel />
      default:
        return <Receipt />
    }
  }

  const formatDate = (dateString) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const handleReorder = (order) => {
    // Add all items from the order back to cart
    order.items.forEach(item => {
      // This would typically use the cart context
      showInfo(`Reorder functionality coming soon`)
    })
  }

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 600, mb: 4 }}>
          {t('orders.title')}
        </Typography>
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
          <Typography variant="h6" color="text.secondary">
            {t('common.loading')}
          </Typography>
        </Box>
      </Container>
    )
  }

  if (orders.length === 0) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <Receipt sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h4" component="h1" sx={{ fontWeight: 600, mb: 2 }}>
              No Orders Yet
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
              You haven't placed any orders yet. Start shopping to see your orders here.
            </Typography>
            <Button
              variant="contained"
              size="large"
              onClick={() => navigate('/products')}
              sx={{ px: 4 }}
            >
              Start Shopping
            </Button>
          </Box>
        </motion.div>
      </Container>
    )
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
          <Typography variant="h4" component="h1" sx={{ fontWeight: 600 }}>
            {t('orders.title')} ({orders.length})
          </Typography>
          <Button
            variant="outlined"
            startIcon={<Refresh />}
            onClick={loadOrders}
          >
            Refresh
          </Button>
        </Box>

        <Grid container spacing={3}>
          {orders.map((order, index) => (
            <Grid item xs={12} key={order.id}>
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: index * 0.1 }}
              >
                <Card sx={{ overflow: 'visible' }}>
                  <CardContent sx={{ p: 3 }}>
                    <Grid container spacing={3} alignItems="center">
                      {/* Order Info */}
                      <Grid item xs={12} md={6}>
                        <Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                            <Typography variant="h6" sx={{ fontWeight: 600, mr: 2 }}>
                              {t('orders.orderNumber')}{order.id}
                            </Typography>
                            <Chip
                              icon={getStatusIcon(order.status)}
                              label={t(`orders.status${order.status.charAt(0).toUpperCase() + order.status.slice(1)}`)}
                              color={getStatusColor(order.status)}
                              size="small"
                            />
                          </Box>

                          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                            <strong>{t('orders.orderDate')}:</strong> {formatDate(order.date)}
                          </Typography>

                          <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                            <strong>{t('orders.total')}:</strong> AED {order.total.toFixed(2)}
                          </Typography>

                          <Typography variant="body2" color="text.secondary">
                            <strong>Items:</strong> {order.items.length} item{order.items.length > 1 ? 's' : ''}
                          </Typography>
                        </Box>
                      </Grid>

                      {/* Order Items Preview */}
                      <Grid item xs={12} md={4}>
                        <Box>
                          <Typography variant="body2" sx={{ fontWeight: 600, mb: 1 }}>
                            Order Items:
                          </Typography>
                          <List dense disablePadding sx={{ maxHeight: 120, overflow: 'auto' }}>
                            {order.items.slice(0, 3).map((item, itemIndex) => (
                              <ListItem key={itemIndex} disablePadding sx={{ mb: 0.5 }}>
                                <ListItemAvatar sx={{ minWidth: 40 }}>
                                  <Avatar
                                    src={item.image}
                                    variant="rounded"
                                    sx={{ width: 32, height: 32 }}
                                  />
                                </ListItemAvatar>
                                <ListItemText
                                  primary={
                                    <Typography variant="body2" sx={{
                                      overflow: 'hidden',
                                      textOverflow: 'ellipsis',
                                      whiteSpace: 'nowrap'
                                    }}>
                                      {item.name}
                                    </Typography>
                                  }
                                  secondary={`${item.quantity} × AED ${item.price.toFixed(2)}`}
                                />
                              </ListItem>
                            ))}
                            {order.items.length > 3 && (
                              <Typography variant="body2" color="text.secondary" sx={{ pl: 5 }}>
                                +{order.items.length - 3} more items
                              </Typography>
                            )}
                          </List>
                        </Box>
                      </Grid>

                      {/* Actions */}
                      <Grid item xs={12} md={2}>
                        <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                          <Button
                            variant="outlined"
                            size="small"
                            startIcon={<Visibility />}
                            onClick={() => navigate(`/orders/${order.id}`)}
                            fullWidth
                          >
                            {t('orders.viewDetails')}
                          </Button>

                          {order.status === 'delivered' && (
                            <Button
                              variant="outlined"
                              size="small"
                              startIcon={<Refresh />}
                              onClick={() => handleReorder(order)}
                              fullWidth
                            >
                              {t('orders.reorder')}
                            </Button>
                          )}
                        </Box>
                      </Grid>
                    </Grid>

                    {/* Delivery Address */}
                    {order.address && (
                      <Box sx={{ mt: 3, pt: 2, borderTop: 1, borderColor: 'divider' }}>
                        <Typography variant="body2" sx={{ fontWeight: 600, mb: 1 }}>
                          {t('orders.deliveryAddress')}:
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {order.address.name} - {order.address.street}, {order.address.city}, {order.address.emirate}
                        </Typography>
                      </Box>
                    )}

                    {/* Estimated Delivery */}
                    {order.estimatedDelivery && order.status !== 'delivered' && order.status !== 'cancelled' && (
                      <Box sx={{ mt: 2 }}>
                        <Typography variant="body2" color="primary" sx={{ fontWeight: 600 }}>
                          Estimated Delivery: {formatDate(order.estimatedDelivery)}
                        </Typography>
                      </Box>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          ))}
        </Grid>

        {/* Order Statistics */}
        <Paper sx={{ p: 3, mt: 4 }}>
          <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
            Order Statistics
          </Typography>
          <Grid container spacing={3}>
            <Grid item xs={6} sm={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="primary" sx={{ fontWeight: 600 }}>
                  {orders.length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Orders
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="success.main" sx={{ fontWeight: 600 }}>
                  {orders.filter(o => o.status === 'delivered').length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Delivered
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="warning.main" sx={{ fontWeight: 600 }}>
                  {orders.filter(o => ['placed', 'confirmed', 'preparing', 'out_for_delivery'].includes(o.status)).length}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  In Progress
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="primary" sx={{ fontWeight: 600 }}>
                  AED {orders.reduce((total, order) => total + order.total, 0).toFixed(2)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Total Spent
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Paper>
      </motion.div>
    </Container>
  )
}

export default OrdersPage
