import * as React from 'react';
import { EmotionJSX } from "./jsx-namespace.js";
export declare const jsx: typeof React.createElement;
export declare namespace jsx {
    namespace JSX {
        type ElementType = EmotionJSX.ElementType;
        interface Element extends EmotionJSX.Element {
        }
        interface ElementClass extends EmotionJSX.ElementClass {
        }
        interface ElementAttributesProperty extends EmotionJSX.ElementAttributesProperty {
        }
        interface ElementChildrenAttribute extends EmotionJSX.ElementChildrenAttribute {
        }
        type LibraryManagedAttributes<C, P> = EmotionJSX.LibraryManagedAttributes<C, P>;
        interface IntrinsicAttributes extends EmotionJSX.IntrinsicAttributes {
        }
        interface IntrinsicClassAttributes<T> extends EmotionJSX.IntrinsicClassAttributes<T> {
        }
        type IntrinsicElements = EmotionJSX.IntrinsicElements;
    }
}
