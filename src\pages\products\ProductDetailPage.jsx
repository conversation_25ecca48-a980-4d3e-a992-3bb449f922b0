import React, { useState, useEffect } from 'react'
import {
  Container,
  Typo<PERSON>,
  Box,
  Grid,
  Card,
  CardMedia,
  Button,
  IconButton,
  Rating,
  Chip,
  Divider,
  List,
  ListItem,
  ListItemText,
  Paper,
  Breadcrumbs,
  Link,
  useTheme,
  useMediaQuery,
  Skeleton
} from '@mui/material'
import {
  Add,
  Remove,
  ShoppingCart,
  Favorite,
  FavoriteBorder,
  Share,
  ArrowBack,
  Star
} from '@mui/icons-material'
import { useParams, useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { motion } from 'framer-motion'

// Data and hooks
import { getProductById, mockProducts } from '../../data/mockProducts'
import { useCart } from '../../hooks/useCart'
import { useNotification } from '../../hooks/useNotification'

const ProductDetailPage = () => {
  const { id } = useParams()
  const navigate = useNavigate()
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  const { t } = useTranslation()
  const { addItem, getItemQuantity, updateQuantity } = useCart()
  const { showSuccess } = useNotification()

  const [product, setProduct] = useState(null)
  const [loading, setLoading] = useState(true)
  const [selectedImage, setSelectedImage] = useState(0)
  const [quantity, setQuantity] = useState(1)
  const [isFavorite, setIsFavorite] = useState(false)

  useEffect(() => {
    const loadProduct = () => {
      setLoading(true)

      // Simulate API call delay
      setTimeout(() => {
        const foundProduct = getProductById(id)
        if (foundProduct) {
          setProduct(foundProduct)
          // Check if product is in favorites (mock)
          const favorites = JSON.parse(localStorage.getItem('jaleelFavorites') || '[]')
          setIsFavorite(favorites.includes(id))
        }
        setLoading(false)
      }, 500)
    }

    loadProduct()
  }, [id])

  const handleAddToCart = () => {
    if (product) {
      addItem(product, quantity)
      showSuccess(t('cart.itemAdded'))
    }
  }

  const handleQuantityChange = (newQuantity) => {
    if (newQuantity >= 1) {
      setQuantity(newQuantity)
    }
  }

  const handleToggleFavorite = () => {
    const favorites = JSON.parse(localStorage.getItem('jaleelFavorites') || '[]')
    let updatedFavorites

    if (isFavorite) {
      updatedFavorites = favorites.filter(fav => fav !== id)
    } else {
      updatedFavorites = [...favorites, id]
    }

    localStorage.setItem('jaleelFavorites', JSON.stringify(updatedFavorites))
    setIsFavorite(!isFavorite)
    showSuccess(isFavorite ? 'Removed from favorites' : 'Added to favorites')
  }

  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: product?.name,
        text: product?.description,
        url: window.location.href,
      })
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href)
      showSuccess('Link copied to clipboard')
    }
  }

  const getRelatedProducts = () => {
    if (!product) return []
    return mockProducts
      .filter(p => p.category === product.category && p.id !== product.id)
      .slice(0, 4)
  }

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Grid container spacing={4}>
          <Grid item xs={12} md={6}>
            <Skeleton variant="rectangular" height={400} />
          </Grid>
          <Grid item xs={12} md={6}>
            <Skeleton variant="text" height={60} />
            <Skeleton variant="text" height={40} />
            <Skeleton variant="text" height={30} />
            <Skeleton variant="rectangular" height={100} />
          </Grid>
        </Grid>
      </Container>
    )
  }

  if (!product) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box sx={{ textAlign: 'center', py: 8 }}>
          <Typography variant="h4" sx={{ mb: 2 }}>
            Product Not Found
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
            The product you're looking for doesn't exist.
          </Typography>
          <Button variant="contained" onClick={() => navigate('/products')}>
            Browse Products
          </Button>
        </Box>
      </Container>
    )
  }

  const currentCartQuantity = getItemQuantity(product.id)
  const isOutOfStock = product.stock === 0

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        {/* Breadcrumbs */}
        <Breadcrumbs sx={{ mb: 3 }}>
          <Link
            color="inherit"
            href="/"
            onClick={(e) => { e.preventDefault(); navigate('/') }}
            sx={{ cursor: 'pointer' }}
          >
            Home
          </Link>
          <Link
            color="inherit"
            href="/products"
            onClick={(e) => { e.preventDefault(); navigate('/products') }}
            sx={{ cursor: 'pointer' }}
          >
            Products
          </Link>
          <Typography color="text.primary">{product.name}</Typography>
        </Breadcrumbs>

        {/* Back Button */}
        <Button
          startIcon={<ArrowBack />}
          onClick={() => navigate(-1)}
          sx={{ mb: 3 }}
        >
          Back
        </Button>

        <Grid container spacing={4}>
          {/* Product Images */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardMedia
                component="img"
                image={product.image}
                alt={product.name}
                sx={{
                  height: { xs: 300, md: 400 },
                  objectFit: 'cover'
                }}
              />
            </Card>
          </Grid>

          {/* Product Info */}
          <Grid item xs={12} md={6}>
            <Box>
              {/* Product Name & Actions */}
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                <Typography variant="h4" component="h1" sx={{ fontWeight: 600, flex: 1 }}>
                  {product.name}
                </Typography>
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <IconButton onClick={handleToggleFavorite} color="primary">
                    {isFavorite ? <Favorite /> : <FavoriteBorder />}
                  </IconButton>
                  <IconButton onClick={handleShare} color="primary">
                    <Share />
                  </IconButton>
                </Box>
              </Box>

              {/* Rating & Reviews */}
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Rating value={product.rating} precision={0.1} readOnly />
                <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                  ({product.reviews} reviews)
                </Typography>
              </Box>

              {/* Brand & Origin */}
              <Typography variant="body1" color="text.secondary" sx={{ mb: 2 }}>
                <strong>Brand:</strong> {product.brand} • <strong>Origin:</strong> {product.origin}
              </Typography>

              {/* Price */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="h3" color="primary" sx={{ fontWeight: 600 }}>
                  AED {product.price.toFixed(2)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {product.unit}
                </Typography>
              </Box>

              {/* Stock Status */}
              <Box sx={{ mb: 3 }}>
                {isOutOfStock ? (
                  <Chip
                    label={t('products.outOfStock')}
                    color="error"
                    size="large"
                  />
                ) : (
                  <Chip
                    label={`${product.stock} ${t('products.inStock')}`}
                    color="success"
                    size="large"
                  />
                )}
              </Box>

              {/* Quantity Selector & Add to Cart */}
              {!isOutOfStock && (
                <Box sx={{ mb: 4 }}>
                  <Typography variant="body1" sx={{ mb: 2, fontWeight: 500 }}>
                    Quantity:
                  </Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 3 }}>
                    <Box sx={{
                      display: 'flex',
                      alignItems: 'center',
                      border: 1,
                      borderColor: 'divider',
                      borderRadius: 1
                    }}>
                      <IconButton
                        onClick={() => handleQuantityChange(quantity - 1)}
                        disabled={quantity <= 1}
                      >
                        <Remove />
                      </IconButton>
                      <Typography variant="h6" sx={{ mx: 2, minWidth: 40, textAlign: 'center' }}>
                        {quantity}
                      </Typography>
                      <IconButton
                        onClick={() => handleQuantityChange(quantity + 1)}
                        disabled={quantity >= product.stock}
                      >
                        <Add />
                      </IconButton>
                    </Box>

                    <Button
                      variant="contained"
                      size="large"
                      startIcon={<ShoppingCart />}
                      onClick={handleAddToCart}
                      sx={{ flex: 1, py: 1.5 }}
                    >
                      Add to Cart - AED {(product.price * quantity).toFixed(2)}
                    </Button>
                  </Box>

                  {currentCartQuantity > 0 && (
                    <Typography variant="body2" color="primary" sx={{ fontWeight: 500 }}>
                      {currentCartQuantity} items already in cart
                    </Typography>
                  )}
                </Box>
              )}

              {/* Product Features */}
              {product.features && (
                <Box sx={{ mb: 3 }}>
                  <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                    Features:
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                    {product.features.map((feature, index) => (
                      <Chip key={index} label={feature} variant="outlined" />
                    ))}
                  </Box>
                </Box>
              )}
            </Box>
          </Grid>
        </Grid>

        {/* Product Description */}
        <Box sx={{ mt: 6 }}>
          <Typography variant="h5" sx={{ mb: 3, fontWeight: 600 }}>
            {t('products.description')}
          </Typography>
          <Paper sx={{ p: 3 }}>
            <Typography variant="body1" sx={{ lineHeight: 1.7 }}>
              {product.description}
            </Typography>
          </Paper>
        </Box>

        {/* Nutritional Information */}
        {product.nutritionalInfo && (
          <Box sx={{ mt: 4 }}>
            <Typography variant="h5" sx={{ mb: 3, fontWeight: 600 }}>
              Nutritional Information
            </Typography>
            <Paper sx={{ p: 3 }}>
              <Grid container spacing={2}>
                {Object.entries(product.nutritionalInfo).map(([key, value]) => (
                  <Grid item xs={6} sm={3} key={key}>
                    <Box sx={{ textAlign: 'center', p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                      <Typography variant="h6" color="primary" sx={{ fontWeight: 600 }}>
                        {value}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" sx={{ textTransform: 'capitalize' }}>
                        {key}
                      </Typography>
                    </Box>
                  </Grid>
                ))}
              </Grid>
            </Paper>
          </Box>
        )}

        {/* Related Products */}
        <Box sx={{ mt: 6 }}>
          <Typography variant="h5" sx={{ mb: 3, fontWeight: 600 }}>
            {t('products.relatedProducts')}
          </Typography>
          <Grid container spacing={3}>
            {getRelatedProducts().map((relatedProduct) => (
              <Grid item xs={12} sm={6} md={3} key={relatedProduct.id}>
                <Card
                  sx={{
                    cursor: 'pointer',
                    transition: 'transform 0.2s',
                    '&:hover': { transform: 'translateY(-4px)' }
                  }}
                  onClick={() => navigate(`/products/${relatedProduct.id}`)}
                >
                  <CardMedia
                    component="img"
                    height="150"
                    image={relatedProduct.image}
                    alt={relatedProduct.name}
                  />
                  <Box sx={{ p: 2 }}>
                    <Typography variant="h6" sx={{
                      fontWeight: 600,
                      mb: 1,
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }}>
                      {relatedProduct.name}
                    </Typography>
                    <Typography variant="h6" color="primary" sx={{ fontWeight: 600 }}>
                      AED {relatedProduct.price.toFixed(2)}
                    </Typography>
                  </Box>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      </motion.div>
    </Container>
  )
}

export default ProductDetailPage
