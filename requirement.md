cash-and-carry-pwa: B2C Grocery/Food PWA Development
Project Name: cash-and-carry-pwa
App Type: Pure ReactJS static app (no Node/Express backend required for the PWA itself)
Deployment Target: Standalone static build, served by an NGINX container
Architecture: Frontend-only PWA. Backend services for actual data persistence and business logic will be handled separately by Go and Java microservices in a future phase.
Development Tools: Docker + Docker Compose for streamlined local development and testing.

Refer:
https://jaleelcashandcarry.com/home

🛠️ Tech Stack & Libraries
ReactJS (latest stable)
MUI (Material UI) for robust component styling and a professional look.
Framer Motion (optional, for smooth UI transitions/animations to enhance user experience).
i18next for localization (l10n).
React Router for efficient client-side navigation.
Dockerfile + docker-compose for containerized development and production builds.

🧩 Feature Requirements
Authentication (Mocked for Demo)
Login / Signup Screens: Implement simple screens with email or phone number input fields.
Simulated Session Access: Upon successful login (e.g., entering any text for username/password, or specific hardcoded credentials), the app should simulate a successful authentication, redirecting the user to protected routes like the product catalog. No actual backend authentication logic is needed in the PWA.
Product Catalog
Product Listing: Display a scrollable list of products, logically grouped by categories. Each product card should show its image, name, price, and unit (e.g., "per kg", "per pack").
Product Detail View: Clicking a product should navigate to a detail page showing a larger image, name, price, unit, and description.
Search Bar: A functional search bar to filter products by name.
Cart
Item Management: Users can add, update the quantity of, and remove items from the cart.
Calculations: Automatically calculate and display the subtotal and total cost of items in the cart.
Checkout
Address Selection (Mocked): Allow users to select a delivery address (can be a mocked list of addresses for the demo).
Order Summary: Present a clear order summary before final placement.
"Place Order" Button: A functional button to submit the order.
Payment Option: Cash on Delivery (COD) as the only payment option. No payment gateway integration is needed for this MVP.
Order History (Mocked for Demo)
"My Orders" View: A dedicated section where users can see a list of their past orders.
Basic Status Display: Each order should display a simple status (e.g., "Placed", "Delivered"). This data can be mocked on the frontend.
Brand Configurator (Admin Settings)
This section is key for impressing Jaleel Holding Company.
Admin Config Interface: Create a hidden or password-protected simple admin-like interface (frontend-only) to configure branding.
Dynamic Customization:
Primary/Secondary Colors: Allow selection of primary and secondary theme colors (e.g., via hex code input). These changes must reflect instantly on the frontend PWA without requiring a rebuild or page reload.
Logo Customization: Enable uploading or inputting a logo image URL. The new logo should update immediately throughout the PWA.
Font Family: Allow selection or input of a font family.
Client-Side Persistence: Configuration changes (colors, logo, font) should persist in the browser (e.g., using localStorage) for demonstration purposes, so they remain even after a browser refresh.
Inventory Management (Mock CSV Reader UI for MVP)
Another crucial impress-factor for Jaleel Holding.
Admin Upload Interface: Provide a simple UI for an administrator to upload a CSV file.
CSV Parsing: The PWA should parse the uploaded CSV file. Expected columns: name, price, image URL, stock, description, category.
Frontend Product Population: The parsed data should populate the PWA's product list, replacing any hardcoded mock data.
Client-Side Persistence: The loaded product data should persist in the browser (e.g., using localStorage) to ensure products remain visible after a refresh.
Stock Indication: Products with stock quantity of 0 should be clearly marked as "Out of Stock" or dynamically hidden from the consumer-facing catalog.
🌍 Internationalization (l10n Support)
i18next Integration: Use i18next for loading language files and managing all text.
Sample Language Files: Provide en.json and ar.json sample files in a /public/locales/ directory.
No Hardcoded Labels: Every UI string, label, and text element must be dynamically loaded from the i18n dictionary.
📱 PWA Features
Installable: Include a manifest.json file to make the app installable on user devices.
Service Worker: Implement a Service Worker for basic offline access and caching of essential assets (e.g., product listing pages, core images, CSS/JS bundles).
Responsive Design: The PWA must be fully responsive and optimized for a seamless experience across all screen sizes (mobile-first approach).
🐳 Docker & Dev Tools
Dockerfile: Create a Dockerfile to build a static production bundle of the React app (npm run build).
NGINX Serving: Use nginx:alpine in the Docker image to serve the build/ folder.
Docker Compose: Provide a docker-compose.yml file to simplify local development, allowing easy spin-up of the PWA.
Scripts: Include standard package.json scripts: dev, build, lint, start.

✅ Final Output Expectations
A fully working, responsive PWA built with ReactJS.
All text and labels dynamically configured via i18n localization files.
Theme colors, logo, and font customizable via the admin configurator with instant, persistent updates.
Product catalog populated dynamically from a mock CSV upload with localStorage persistence.
Basic user feedback: Simple alerts or toasts for actions like "Product added to cart!" or "Order Placed!".
A clean, static build/ folder ready for deployment via NGINX.
All project code contained within a single Replit project, runnable via Docker Compose.

This detailed prompt should guide the AI effectively in creating a compelling demo for Jaleel Holding Company!

