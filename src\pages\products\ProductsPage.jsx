import React, { useState, useEffect } from 'react'
import {
  Container,
  Typo<PERSON>,
  Box,
  Grid,
  Card,
  CardMedia,
  CardContent,
  CardActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Rating,
  IconButton,
  Badge,
  InputAdornment,
  useTheme,
  useMediaQuery
} from '@mui/material'
import {
  Search,
  Add,
  Remove,
  ShoppingCart,
  FilterList
} from '@mui/icons-material'
import { useTranslation } from 'react-i18next'
import { motion } from 'framer-motion'
import { useNavigate } from 'react-router-dom'

// Data and hooks
import { getAllProducts, getAllCategories, getProductsByCategory, searchProducts } from '../../data/mockProducts'
import { useCart } from '../../hooks/useCart'
import { useNotification } from '../../hooks/useNotification'

const ProductsPage = () => {
  const navigate = useNavigate()
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  const { t } = useTranslation()
  const { addItem, getItemQuantity, updateQuantity } = useCart()
  const { showSuccess } = useNotification()

  const [products, setProducts] = useState([])
  const [categories, setCategories] = useState(['All Categories'])
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('All Categories')
  const [loading, setLoading] = useState(false)

  // Load categories on mount
  useEffect(() => {
    setCategories(getAllCategories())
  }, [])

  // Filter products based on search and category
  useEffect(() => {
    setLoading(true)
    let filteredProducts = getAllProducts()

    // Apply category filter
    if (selectedCategory !== 'All Categories') {
      filteredProducts = getProductsByCategory(selectedCategory)
    }

    // Apply search filter
    if (searchQuery.trim()) {
      filteredProducts = searchProducts(searchQuery).filter(product =>
        selectedCategory === 'All Categories' || product.category === selectedCategory
      )
    }

    // Simulate loading delay
    setTimeout(() => {
      setProducts(filteredProducts)
      setLoading(false)
    }, 300)
  }, [searchQuery, selectedCategory])

  const handleAddToCart = (product) => {
    addItem(product, 1)
    showSuccess(t('cart.itemAdded'))
  }

  const handleQuantityChange = (product, newQuantity) => {
    if (newQuantity === 0) {
      updateQuantity(product.id, 0)
    } else {
      updateQuantity(product.id, newQuantity)
    }
  }

  const ProductCard = ({ product }) => {
    const currentQuantity = getItemQuantity(product.id)
    const isOutOfStock = product.stock === 0

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.4 }}
      >
        <Card
          sx={{
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            cursor: 'pointer',
            transition: 'transform 0.2s, box-shadow 0.2s',
            '&:hover': {
              transform: 'translateY(-4px)',
              boxShadow: 4
            },
            opacity: isOutOfStock ? 0.6 : 1
          }}
        >
          <Box onClick={() => navigate(`/products/${product.id}`)}>
            <CardMedia
              component="img"
              height="200"
              image={product.image}
              alt={product.name}
              sx={{ objectFit: 'cover' }}
            />
            <CardContent sx={{ flexGrow: 1, pb: 1 }}>
              <Typography variant="h6" component="h3" sx={{
                fontWeight: 600,
                mb: 1,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical'
              }}>
                {product.name}
              </Typography>

              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Rating value={product.rating} precision={0.1} size="small" readOnly />
                <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                  ({product.reviews})
                </Typography>
              </Box>

              <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                {product.brand} • {product.origin}
              </Typography>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                <Typography variant="h6" color="primary" sx={{ fontWeight: 600 }}>
                  AED {product.price.toFixed(2)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {product.unit}
                </Typography>
              </Box>

              {isOutOfStock ? (
                <Chip
                  label={t('products.outOfStock')}
                  color="error"
                  size="small"
                  sx={{ mb: 1 }}
                />
              ) : (
                <Chip
                  label={`${product.stock} ${t('products.inStock')}`}
                  color="success"
                  size="small"
                  sx={{ mb: 1 }}
                />
              )}
            </CardContent>
          </Box>

          <CardActions sx={{ p: 2, pt: 0 }}>
            {currentQuantity > 0 ? (
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                width: '100%'
              }}>
                <IconButton
                  size="small"
                  onClick={() => handleQuantityChange(product, currentQuantity - 1)}
                  disabled={isOutOfStock}
                >
                  <Remove />
                </IconButton>
                <Typography variant="body1" sx={{ mx: 2, fontWeight: 600 }}>
                  {currentQuantity}
                </Typography>
                <IconButton
                  size="small"
                  onClick={() => handleQuantityChange(product, currentQuantity + 1)}
                  disabled={isOutOfStock}
                >
                  <Add />
                </IconButton>
              </Box>
            ) : (
              <Button
                fullWidth
                variant="contained"
                startIcon={<ShoppingCart />}
                onClick={() => handleAddToCart(product)}
                disabled={isOutOfStock}
              >
                {t('products.addToCart')}
              </Button>
            )}
          </CardActions>
        </Card>
      </motion.div>
    )
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" sx={{ fontWeight: 600, mb: 2 }}>
          {t('products.title')}
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Discover our wide range of quality products
        </Typography>
      </Box>

      {/* Search and Filters */}
      <Box sx={{ mb: 4 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder={t('products.searchPlaceholder')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel>{t('products.categories')}</InputLabel>
              <Select
                value={selectedCategory}
                label={t('products.categories')}
                onChange={(e) => setSelectedCategory(e.target.value)}
              >
                {categories.map((category) => (
                  <MenuItem key={category} value={category}>
                    {category}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} md={2}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<FilterList />}
              sx={{ height: 56 }}
            >
              {t('common.filter')}
            </Button>
          </Grid>
        </Grid>
      </Box>

      {/* Results Count */}
      <Box sx={{ mb: 3 }}>
        <Typography variant="body2" color="text.secondary">
          {loading ? 'Loading...' : `${products.length} products found`}
        </Typography>
      </Box>

      {/* Products Grid */}
      {loading ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
          <Typography variant="h6" color="text.secondary">
            {t('common.loading')}
          </Typography>
        </Box>
      ) : products.length === 0 ? (
        <Box sx={{ textAlign: 'center', py: 8 }}>
          <Typography variant="h6" color="text.secondary" sx={{ mb: 2 }}>
            No products found
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Try adjusting your search or filter criteria
          </Typography>
        </Box>
      ) : (
        <Grid container spacing={3}>
          {products.map((product) => (
            <Grid item xs={12} sm={6} md={4} lg={3} key={product.id}>
              <ProductCard product={product} />
            </Grid>
          ))}
        </Grid>
      )}
    </Container>
  )
}

export default ProductsPage
