// Mock product data for the PWA
export const mockProducts = [
  {
    id: '1',
    name: 'Fresh Bananas',
    price: 8.50,
    unit: 'per kg',
    category: 'Fresh Fruits',
    image: 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=400&h=300&fit=crop',
    description: 'Fresh, ripe bananas imported from the best farms. Perfect for snacking or cooking.',
    stock: 150,
    rating: 4.5,
    reviews: 23,
    brand: 'Fresh Select',
    origin: 'Ecuador',
    nutritionalInfo: {
      calories: 89,
      protein: '1.1g',
      carbs: '23g',
      fiber: '2.6g'
    }
  },
  {
    id: '2',
    name: 'Basmati Rice 5kg',
    price: 45.00,
    unit: 'per pack',
    category: 'Pantry Essentials',
    image: 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=400&h=300&fit=crop',
    description: 'Premium quality Basmati rice, aged for perfect aroma and taste.',
    stock: 75,
    rating: 4.8,
    reviews: 156,
    brand: 'Royal Basmati',
    origin: 'India',
    features: ['Long grain', 'Aged rice', 'Premium quality']
  },
  {
    id: '3',
    name: 'Fresh Milk 1L',
    price: 6.50,
    unit: 'per bottle',
    category: 'Dairy & Frozen',
    image: 'https://images.unsplash.com/photo-1563636619-e9143da7973b?w=400&h=300&fit=crop',
    description: 'Fresh, pasteurized whole milk from local farms.',
    stock: 200,
    rating: 4.3,
    reviews: 89,
    brand: 'Farm Fresh',
    origin: 'UAE',
    expiryDays: 5,
    nutritionalInfo: {
      calories: 150,
      protein: '8g',
      fat: '8g',
      calcium: '276mg'
    }
  },
  {
    id: '4',
    name: 'Olive Oil 500ml',
    price: 28.00,
    unit: 'per bottle',
    category: 'Pantry Essentials',
    image: 'https://images.unsplash.com/photo-1474979266404-7eaacbcd87c5?w=400&h=300&fit=crop',
    description: 'Extra virgin olive oil, cold-pressed for maximum flavor and nutrition.',
    stock: 45,
    rating: 4.7,
    reviews: 67,
    brand: 'Mediterranean Gold',
    origin: 'Spain',
    features: ['Extra Virgin', 'Cold Pressed', 'Premium Quality']
  },
  {
    id: '5',
    name: 'Chicken Breast 1kg',
    price: 32.00,
    unit: 'per kg',
    category: 'Fresh Meat',
    image: 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?w=400&h=300&fit=crop',
    description: 'Fresh, boneless chicken breast from free-range chickens.',
    stock: 25,
    rating: 4.6,
    reviews: 45,
    brand: 'Farm Fresh',
    origin: 'UAE',
    features: ['Boneless', 'Free Range', 'Halal Certified']
  },
  {
    id: '6',
    name: 'Orange Juice 1L',
    price: 12.50,
    unit: 'per bottle',
    category: 'Beverages',
    image: 'https://images.unsplash.com/photo-1544145945-f90425340c7e?w=400&h=300&fit=crop',
    description: '100% pure orange juice, no added sugar or preservatives.',
    stock: 80,
    rating: 4.4,
    reviews: 34,
    brand: 'Pure Citrus',
    origin: 'Brazil',
    features: ['100% Pure', 'No Added Sugar', 'Vitamin C Rich']
  },
  {
    id: '7',
    name: 'Tomatoes',
    price: 7.50,
    unit: 'per kg',
    category: 'Fresh Vegetables',
    image: 'https://images.unsplash.com/photo-1592924357228-91a4daadcfea?w=400&h=300&fit=crop',
    description: 'Fresh, ripe tomatoes perfect for cooking and salads.',
    stock: 120,
    rating: 4.2,
    reviews: 78,
    brand: 'Garden Fresh',
    origin: 'Jordan',
    nutritionalInfo: {
      calories: 18,
      protein: '0.9g',
      carbs: '3.9g',
      vitaminC: '14mg'
    }
  },
  {
    id: '8',
    name: 'Bread Loaf',
    price: 4.50,
    unit: 'per loaf',
    category: 'Bakery',
    image: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400&h=300&fit=crop',
    description: 'Fresh white bread, baked daily in our local bakery.',
    stock: 60,
    rating: 4.1,
    reviews: 92,
    brand: 'Daily Bread',
    origin: 'UAE',
    features: ['Freshly Baked', 'Soft Texture', 'No Preservatives']
  },
  {
    id: '9',
    name: 'Eggs 12 pieces',
    price: 9.00,
    unit: 'per dozen',
    category: 'Dairy & Frozen',
    image: 'https://images.unsplash.com/photo-1582722872445-44dc5f7e3c8f?w=400&h=300&fit=crop',
    description: 'Fresh farm eggs from free-range chickens.',
    stock: 90,
    rating: 4.5,
    reviews: 67,
    brand: 'Farm Fresh',
    origin: 'UAE',
    features: ['Free Range', 'Large Size', 'Fresh Daily']
  },
  {
    id: '10',
    name: 'Apples Red',
    price: 12.00,
    unit: 'per kg',
    category: 'Fresh Fruits',
    image: 'https://images.unsplash.com/photo-1560806887-1e4cd0b6cbd6?w=400&h=300&fit=crop',
    description: 'Crisp, sweet red apples imported from premium orchards.',
    stock: 85,
    rating: 4.6,
    reviews: 54,
    brand: 'Premium Select',
    origin: 'USA',
    nutritionalInfo: {
      calories: 52,
      protein: '0.3g',
      carbs: '14g',
      fiber: '2.4g'
    }
  },
  {
    id: '11',
    name: 'Pasta 500g',
    price: 8.50,
    unit: 'per pack',
    category: 'Pantry Essentials',
    image: 'https://images.unsplash.com/photo-1551892374-ecf8754cf8b0?w=400&h=300&fit=crop',
    description: 'Premium durum wheat pasta, perfect for all your favorite recipes.',
    stock: 110,
    rating: 4.3,
    reviews: 89,
    brand: 'Italian Choice',
    origin: 'Italy',
    features: ['Durum Wheat', 'Al Dente Texture', 'Premium Quality']
  },
  {
    id: '12',
    name: 'Yogurt 500g',
    price: 8.00,
    unit: 'per container',
    category: 'Dairy & Frozen',
    image: 'https://images.unsplash.com/photo-1488477181946-6428a0291777?w=400&h=300&fit=crop',
    description: 'Creamy natural yogurt with live cultures, perfect for breakfast.',
    stock: 70,
    rating: 4.4,
    reviews: 43,
    brand: 'Creamy Delight',
    origin: 'UAE',
    features: ['Live Cultures', 'Natural', 'Probiotic']
  }
]

export const categories = [
  'All Categories',
  'Fresh Fruits',
  'Fresh Vegetables', 
  'Dairy & Frozen',
  'Pantry Essentials',
  'Fresh Meat',
  'Beverages',
  'Bakery'
]

// Function to get products by category
export const getProductsByCategory = (category) => {
  if (category === 'All Categories') {
    return mockProducts
  }
  return mockProducts.filter(product => product.category === category)
}

// Function to search products
export const searchProducts = (query) => {
  const lowercaseQuery = query.toLowerCase()
  return mockProducts.filter(product => 
    product.name.toLowerCase().includes(lowercaseQuery) ||
    product.category.toLowerCase().includes(lowercaseQuery) ||
    product.brand.toLowerCase().includes(lowercaseQuery)
  )
}

// Function to get product by ID
export const getProductById = (id) => {
  return mockProducts.find(product => product.id === id)
}
