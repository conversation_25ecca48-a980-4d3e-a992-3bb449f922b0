# Jaleel Cash & Carry PWA

A comprehensive B2C Progressive Web Application for Jaleel Cash & Carry, built with ReactJS, Material-UI, and modern web technologies.

## 🚀 Features

### Core Features
- **Progressive Web App (PWA)** - Installable, offline-capable
- **Responsive Design** - Mobile-first approach with seamless desktop experience
- **Internationalization** - English and Arabic language support
- **Authentication** - Mock login/signup with session management
- **Product Catalog** - Browse, search, and filter products
- **Shopping Cart** - Add, remove, and manage cart items
- **Checkout Process** - Multi-step checkout with address selection
- **Order Management** - View order history and track orders
- **User Profile** - Manage personal information and addresses

### Admin Features (Key Differentiators)
- **Brand Configurator** - Real-time theme customization
  - Dynamic primary/secondary colors
  - Logo customization with instant preview
  - Font family selection
  - Live preview without rebuild
- **Inventory Management** - CSV upload for product management
  - Parse and validate CSV files
  - Dynamic product catalog updates
  - Stock management with out-of-stock indicators
  - Client-side persistence

### Technical Features
- **Modern React Stack** - React 18, Vite, Material-UI
- **State Management** - Context API with custom hooks
- **Animations** - Framer Motion for smooth transitions
- **Offline Support** - Service Worker for caching
- **Local Storage** - Persistent cart, orders, and configuration
- **Docker Support** - Containerized development and production

## 🛠️ Tech Stack

- **Frontend**: ReactJS 18, Material-UI (MUI), Framer Motion
- **Build Tool**: Vite
- **Internationalization**: i18next
- **Routing**: React Router DOM
- **CSV Processing**: PapaParse
- **Containerization**: Docker + Docker Compose
- **Web Server**: NGINX (production)

## 📦 Installation & Setup

### Prerequisites
- Node.js 18+ 
- Docker & Docker Compose (optional)

### Local Development

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd cash-and-carry-pwa
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm run dev
   ```

4. **Open browser**
   Navigate to `http://localhost:3000`

### Docker Development

1. **Build and run with Docker Compose**
   ```bash
   docker-compose up --build
   ```

2. **Access the application**
   - Development: `http://localhost:3001`
   - Production: `http://localhost:3000`

## 🎯 Demo Accounts

### Customer Account
- **Email**: `<EMAIL>`
- **Password**: `demo123`

### Admin Account
- **Email**: `<EMAIL>`
- **Password**: `admin123`

## 📱 PWA Features

- **Installable** - Add to home screen on mobile/desktop
- **Offline Support** - Basic functionality works offline
- **Push Notifications** - Ready for implementation
- **App-like Experience** - Full-screen, native feel

## 🎨 Brand Configurator

The admin panel includes a powerful brand configurator that allows real-time customization:

1. **Access Admin Panel** - Login with admin credentials
2. **Brand Configurator Tab** - Customize colors, logo, fonts
3. **Live Preview** - See changes instantly
4. **Persistent Storage** - Configuration saved in localStorage

### Supported Customizations
- Primary and secondary theme colors
- Logo URL with instant preview
- Font family selection from predefined options
- Real-time theme updates without page reload

## 📊 Inventory Management

Upload and manage products via CSV:

1. **CSV Format**: `name,price,imageUrl,stock,description,category`
2. **Upload Process** - Drag & drop or file selection
3. **Validation** - Automatic data validation and error handling
4. **Integration** - Uploaded products appear in main catalog
5. **Persistence** - Data stored in localStorage

### Sample CSV Format
```csv
name,price,imageUrl,stock,description,category
Fresh Bananas,8.50,https://example.com/banana.jpg,150,Fresh ripe bananas,Fresh Fruits
Basmati Rice,45.00,https://example.com/rice.jpg,75,Premium basmati rice,Pantry Essentials
```

## 🌍 Internationalization

- **Languages**: English (default), Arabic
- **RTL Support** - Automatic layout direction for Arabic
- **Dynamic Switching** - Language selector in navigation
- **Persistent Preference** - Language choice saved locally

## 📱 Mobile Experience

- **Bottom Navigation** - Easy thumb navigation on mobile
- **Touch-Friendly** - Optimized touch targets
- **Responsive Grid** - Adaptive product layouts
- **Mobile-First** - Designed for mobile, enhanced for desktop

## 🔧 Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## 🏗️ Project Structure

```
src/
├── components/          # Reusable components
│   └── layout/         # Layout components (Navbar, Footer, etc.)
├── contexts/           # React Context providers
├── data/              # Mock data and utilities
├── hooks/             # Custom React hooks
├── i18n/              # Internationalization setup
├── pages/             # Page components
│   ├── auth/          # Authentication pages
│   ├── products/      # Product-related pages
│   ├── cart/          # Shopping cart
│   ├── checkout/      # Checkout process
│   ├── orders/        # Order management
│   ├── profile/       # User profile
│   └── admin/         # Admin panel
├── theme/             # Material-UI theme configuration
└── App.jsx            # Main application component
```

## 🚀 Deployment

### Production Build
```bash
npm run build
```

### Docker Production
```bash
docker build -t jaleel-pwa .
docker run -p 3000:80 jaleel-pwa
```

## 🎯 Key Business Features

1. **Professional Branding** - Jaleel Cash & Carry visual identity
2. **B2C Focus** - Consumer-friendly shopping experience
3. **UAE Market** - Localized for UAE customers
4. **Wholesale Heritage** - Leveraging 50+ years of trust
5. **Modern Technology** - Progressive web app capabilities

## 📈 Future Enhancements

- Real backend API integration
- Payment gateway integration
- Push notifications
- Advanced analytics
- Social media integration
- Enhanced search with filters
- Wishlist functionality
- Product reviews and ratings

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📄 License

This project is proprietary software for Jaleel Holdings.

---

**Built with ❤️ for Jaleel Cash & Carry**
