{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "confirm": "Confirm", "save": "Save", "edit": "Edit", "delete": "Delete", "search": "Search", "filter": "Filter", "sort": "Sort", "back": "Back", "next": "Next", "previous": "Previous", "home": "Home", "logout": "Logout"}, "navigation": {"home": "Home", "products": "Products", "cart": "<PERSON><PERSON>", "orders": "My Orders", "profile": "Profile", "admin": "Admin"}, "auth": {"login": "<PERSON><PERSON>", "signup": "Sign Up", "email": "Email", "phone": "Phone Number", "password": "Password", "confirmPassword": "Confirm Password", "forgotPassword": "Forgot Password?", "loginSuccess": "Login successful!", "loginError": "Invalid credentials", "signupSuccess": "Account created successfully!", "emailPlaceholder": "Enter your email", "phonePlaceholder": "Enter your phone number", "passwordPlaceholder": "Enter your password"}, "products": {"title": "Products", "searchPlaceholder": "Search products...", "categories": "Categories", "allCategories": "All Categories", "price": "Price", "unit": "Unit", "addToCart": "Add to Cart", "outOfStock": "Out of Stock", "inStock": "In Stock", "productDetails": "Product Details", "description": "Description", "specifications": "Specifications", "reviews": "Reviews", "relatedProducts": "Related Products"}, "cart": {"title": "Shopping Cart", "empty": "Your cart is empty", "quantity": "Quantity", "subtotal": "Subtotal", "total": "Total", "checkout": "Checkout", "removeItem": "Remove Item", "updateQuantity": "Update Quantity", "itemAdded": "Item added to cart!", "itemRemoved": "Item removed from cart", "itemUpdated": "Cart updated"}, "checkout": {"title": "Checkout", "deliveryAddress": "Delivery Address", "selectAddress": "Select Address", "addNewAddress": "Add New Address", "orderSummary": "Order Summary", "paymentMethod": "Payment Method", "cashOnDelivery": "Cash on Delivery", "placeOrder": "Place Order", "orderPlaced": "Order placed successfully!", "orderTotal": "Order Total", "deliveryFee": "Delivery Fee", "free": "Free"}, "orders": {"title": "My Orders", "orderNumber": "Order #", "orderDate": "Order Date", "status": "Status", "total": "Total", "viewDetails": "View Details", "trackOrder": "Track Order", "reorder": "Reorder", "orderDetails": "Order Details", "deliveryAddress": "Delivery Address", "orderItems": "Order Items", "statusPlaced": "Placed", "statusConfirmed": "Confirmed", "statusPreparing": "Preparing", "statusOutForDelivery": "Out for Delivery", "statusDelivered": "Delivered", "statusCancelled": "Cancelled"}, "admin": {"title": "Admin Panel", "brandConfigurator": "Brand Configurator", "inventoryManagement": "Inventory Management", "primaryColor": "Primary Color", "secondaryColor": "Secondary Color", "logo": "Logo", "fontFamily": "Font Family", "uploadLogo": "Upload Logo", "logoUrl": "Logo URL", "previewChanges": "Preview Changes", "saveChanges": "Save Changes", "resetToDefault": "Reset to De<PERSON>ult", "uploadCsv": "Upload CSV", "csvFormat": "CSV Format: name, price, imageUrl, stock, description, category", "productsUploaded": "Products uploaded successfully!", "invalidCsv": "Invalid CSV format", "configSaved": "Configuration saved successfully!", "configReset": "Configuration reset to default"}, "footer": {"aboutUs": "About Us", "contactUs": "Contact Us", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "followUs": "Follow Us", "copyright": "© 2024 Jaleel Cash & Carry. All rights reserved.", "poweredBy": "A Subsidiary of Jaleel Holdings"}}