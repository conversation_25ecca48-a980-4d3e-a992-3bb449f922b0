import React from 'react'
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  CardMedia,
  IconButton,
  Button,
  Divider,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  useTheme,
  useMediaQuery
} from '@mui/material'
import {
  Add,
  Remove,
  Delete,
  ShoppingCartOutlined,
  ArrowForward
} from '@mui/icons-material'
import { useTranslation } from 'react-i18next'
import { motion } from 'framer-motion'
import { useNavigate } from 'react-router-dom'

// Hooks
import { useCart } from '../../hooks/useCart'
import { useNotification } from '../../hooks/useNotification'
import { useAuth } from '../../hooks/useAuth'

const CartPage = () => {
  const navigate = useNavigate()
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  const { t } = useTranslation()
  const {
    items,
    removeItem,
    updateQuantity,
    getSubtotal,
    getTotal,
    getDeliveryFee,
    clearCart
  } = useCart()
  const { showSuccess, showInfo } = useNotification()
  const { isAuthenticated } = useAuth()

  const handleQuantityChange = (productId, newQuantity) => {
    if (newQuantity === 0) {
      removeItem(productId)
      showInfo(t('cart.itemRemoved'))
    } else {
      updateQuantity(productId, newQuantity)
      showSuccess(t('cart.itemUpdated'))
    }
  }

  const handleRemoveItem = (productId) => {
    removeItem(productId)
    showInfo(t('cart.itemRemoved'))
  }

  const handleCheckout = () => {
    if (!isAuthenticated) {
      navigate('/login', { state: { from: { pathname: '/checkout' } } })
      return
    }
    navigate('/checkout')
  }

  const CartItem = ({ item }) => (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: 20 }}
      transition={{ duration: 0.3 }}
    >
      <Card sx={{ mb: 2, overflow: 'visible' }}>
        <CardContent sx={{ p: 2 }}>
          <Grid container spacing={2} alignItems="center">
            {/* Product Image */}
            <Grid item xs={3} sm={2}>
              <CardMedia
                component="img"
                image={item.image}
                alt={item.name}
                sx={{
                  width: '100%',
                  height: 80,
                  objectFit: 'cover',
                  borderRadius: 1,
                  cursor: 'pointer'
                }}
                onClick={() => navigate(`/products/${item.id}`)}
              />
            </Grid>

            {/* Product Details */}
            <Grid item xs={9} sm={4}>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  mb: 0.5,
                  cursor: 'pointer',
                  '&:hover': { color: 'primary.main' }
                }}
                onClick={() => navigate(`/products/${item.id}`)}
              >
                {item.name}
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                {item.brand} • {item.unit}
              </Typography>
              <Typography variant="h6" color="primary" sx={{ fontWeight: 600 }}>
                AED {item.price.toFixed(2)}
              </Typography>
            </Grid>

            {/* Quantity Controls */}
            <Grid item xs={6} sm={3}>
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                border: 1,
                borderColor: 'divider',
                borderRadius: 1,
                p: 0.5
              }}>
                <IconButton
                  size="small"
                  onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                >
                  <Remove />
                </IconButton>
                <Typography variant="body1" sx={{ mx: 2, fontWeight: 600, minWidth: 20, textAlign: 'center' }}>
                  {item.quantity}
                </Typography>
                <IconButton
                  size="small"
                  onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                >
                  <Add />
                </IconButton>
              </Box>
            </Grid>

            {/* Total & Remove */}
            <Grid item xs={6} sm={3}>
              <Box sx={{ textAlign: 'right' }}>
                <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
                  AED {(item.price * item.quantity).toFixed(2)}
                </Typography>
                <IconButton
                  color="error"
                  size="small"
                  onClick={() => handleRemoveItem(item.id)}
                >
                  <Delete />
                </IconButton>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </motion.div>
  )

  // Empty cart state
  if (items.length === 0) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <Box sx={{ textAlign: 'center', py: 8 }}>
            <ShoppingCartOutlined sx={{ fontSize: 80, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h4" component="h1" sx={{ fontWeight: 600, mb: 2 }}>
              {t('cart.empty')}
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
              Add some products to your cart to get started
            </Typography>
            <Button
              variant="contained"
              size="large"
              onClick={() => navigate('/products')}
              sx={{ px: 4 }}
            >
              {t('products.title')}
            </Button>
          </Box>
        </motion.div>
      </Container>
    )
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Typography variant="h4" component="h1" sx={{ fontWeight: 600, mb: 4 }}>
          {t('cart.title')} ({items.length} items)
        </Typography>

        <Grid container spacing={4}>
          {/* Cart Items */}
          <Grid item xs={12} md={8}>
            <Box>
              {items.map((item) => (
                <CartItem key={item.id} item={item} />
              ))}

              {/* Clear Cart Button */}
              <Box sx={{ mt: 3, textAlign: 'right' }}>
                <Button
                  variant="outlined"
                  color="error"
                  onClick={clearCart}
                  startIcon={<Delete />}
                >
                  Clear Cart
                </Button>
              </Box>
            </Box>
          </Grid>

          {/* Order Summary */}
          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 3, position: 'sticky', top: 100 }}>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
                Order Summary
              </Typography>

              <List disablePadding>
                <ListItem sx={{ px: 0 }}>
                  <ListItemText primary={t('cart.subtotal')} />
                  <Typography variant="body1" sx={{ fontWeight: 600 }}>
                    AED {getSubtotal().toFixed(2)}
                  </Typography>
                </ListItem>

                <ListItem sx={{ px: 0 }}>
                  <ListItemText
                    primary="Delivery Fee"
                    secondary={getSubtotal() > 100 ? "Free delivery on orders over AED 100" : ""}
                  />
                  <Typography variant="body1" sx={{ fontWeight: 600 }}>
                    {getDeliveryFee() === 0 ? 'Free' : `AED ${getDeliveryFee().toFixed(2)}`}
                  </Typography>
                </ListItem>

                <Divider sx={{ my: 2 }} />

                <ListItem sx={{ px: 0 }}>
                  <ListItemText
                    primary={
                      <Typography variant="h6" sx={{ fontWeight: 600 }}>
                        {t('cart.total')}
                      </Typography>
                    }
                  />
                  <Typography variant="h6" sx={{ fontWeight: 600, color: 'primary.main' }}>
                    AED {getTotal().toFixed(2)}
                  </Typography>
                </ListItem>
              </List>

              <Button
                fullWidth
                variant="contained"
                size="large"
                onClick={handleCheckout}
                endIcon={<ArrowForward />}
                sx={{ mt: 3, py: 1.5 }}
              >
                {t('cart.checkout')}
              </Button>

              <Button
                fullWidth
                variant="outlined"
                onClick={() => navigate('/products')}
                sx={{ mt: 2 }}
              >
                Continue Shopping
              </Button>
            </Paper>
          </Grid>
        </Grid>
      </motion.div>
    </Container>
  )
}

export default CartPage
