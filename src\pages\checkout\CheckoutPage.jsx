import React, { useState } from 'react'
import {
  Container,
  <PERSON>po<PERSON>,
  <PERSON>,
  Grid,
  Card,
  CardContent,
  <PERSON><PERSON>,
  <PERSON>per,
  Step,
  StepLabel,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemAvatar,
  Avatar,
  Divider,
  Radio,
  RadioGroup,
  FormControlLabel,
  FormControl,
  FormLabel,
  TextField,
  Alert,
  CircularProgress,
  useTheme,
  useMediaQuery
} from '@mui/material'
import {
  LocationOn,
  Payment,
  CheckCircle,
  ArrowBack,
  ArrowForward,
  Add,
  Edit
} from '@mui/icons-material'
import { useNavigate } from 'react-router-dom'
import { useTranslation } from 'react-i18next'
import { motion } from 'framer-motion'

// Hooks
import { useCart } from '../../hooks/useCart'
import { useAuth } from '../../hooks/useAuth'
import { useNotification } from '../../hooks/useNotification'

const CheckoutPage = () => {
  const navigate = useNavigate()
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('md'))
  const { t } = useTranslation()
  const { items, getSubtotal, getTotal, getDeliveryFee, clearCart } = useCart()
  const { user } = useAuth()
  const { showSuccess, showError } = useNotification()

  const [activeStep, setActiveStep] = useState(0)
  const [selectedAddress, setSelectedAddress] = useState(user?.addresses?.[0]?.id || '')
  const [paymentMethod, setPaymentMethod] = useState('cod')
  const [loading, setLoading] = useState(false)
  const [orderPlaced, setOrderPlaced] = useState(false)
  const [orderId, setOrderId] = useState('')

  const steps = [
    t('checkout.deliveryAddress'),
    t('checkout.paymentMethod'),
    t('checkout.orderSummary')
  ]

  // Redirect if cart is empty
  if (items.length === 0 && !orderPlaced) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <Box sx={{ textAlign: 'center', py: 8 }}>
          <Typography variant="h4" sx={{ mb: 2 }}>
            Your cart is empty
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
            Add some products to your cart before checkout
          </Typography>
          <Button variant="contained" onClick={() => navigate('/products')}>
            Browse Products
          </Button>
        </Box>
      </Container>
    )
  }

  const handleNext = () => {
    if (activeStep === 0 && !selectedAddress) {
      showError('Please select a delivery address')
      return
    }
    if (activeStep === 1 && !paymentMethod) {
      showError('Please select a payment method')
      return
    }
    if (activeStep === 2) {
      handlePlaceOrder()
      return
    }
    setActiveStep((prevStep) => prevStep + 1)
  }

  const handleBack = () => {
    setActiveStep((prevStep) => prevStep - 1)
  }

  const handlePlaceOrder = async () => {
    setLoading(true)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))

      const newOrderId = `JCC-${Date.now()}`
      setOrderId(newOrderId)

      // Save order to localStorage (mock)
      const order = {
        id: newOrderId,
        items: items,
        subtotal: getSubtotal(),
        deliveryFee: getDeliveryFee(),
        total: getTotal(),
        address: user?.addresses?.find(addr => addr.id === selectedAddress),
        paymentMethod: paymentMethod,
        status: 'placed',
        date: new Date().toISOString(),
        estimatedDelivery: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      }

      const existingOrders = JSON.parse(localStorage.getItem('jaleelOrders') || '[]')
      existingOrders.unshift(order)
      localStorage.setItem('jaleelOrders', JSON.stringify(existingOrders))

      clearCart()
      setOrderPlaced(true)
      showSuccess(t('checkout.orderPlaced'))
    } catch (error) {
      showError('Failed to place order. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  const getSelectedAddress = () => {
    return user?.addresses?.find(addr => addr.id === selectedAddress)
  }

  // Order Success Screen
  if (orderPlaced) {
    return (
      <Container maxWidth="md" sx={{ py: 4 }}>
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6 }}
        >
          <Paper sx={{ p: 4, textAlign: 'center' }}>
            <CheckCircle sx={{ fontSize: 80, color: 'success.main', mb: 2 }} />
            <Typography variant="h4" sx={{ fontWeight: 600, mb: 2 }}>
              Order Placed Successfully!
            </Typography>
            <Typography variant="h6" color="primary" sx={{ mb: 2 }}>
              Order ID: {orderId}
            </Typography>
            <Typography variant="body1" color="text.secondary" sx={{ mb: 4 }}>
              Thank you for your order. We'll send you a confirmation email shortly.
            </Typography>

            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center' }}>
              <Button
                variant="contained"
                onClick={() => navigate('/orders')}
                sx={{ px: 4 }}
              >
                View Orders
              </Button>
              <Button
                variant="outlined"
                onClick={() => navigate('/products')}
                sx={{ px: 4 }}
              >
                Continue Shopping
              </Button>
            </Box>
          </Paper>
        </motion.div>
      </Container>
    )
  }

  const AddressStep = () => (
    <Box>
      <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
        {t('checkout.selectAddress')}
      </Typography>

      <FormControl component="fieldset" fullWidth>
        <RadioGroup
          value={selectedAddress}
          onChange={(e) => setSelectedAddress(e.target.value)}
        >
          {user?.addresses?.map((address) => (
            <Card key={address.id} sx={{ mb: 2 }}>
              <CardContent>
                <FormControlLabel
                  value={address.id}
                  control={<Radio />}
                  label={
                    <Box sx={{ ml: 1 }}>
                      <Typography variant="h6" sx={{ fontWeight: 600 }}>
                        {address.name}
                        {address.isDefault && (
                          <Typography component="span" variant="body2" color="primary" sx={{ ml: 1 }}>
                            (Default)
                          </Typography>
                        )}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {address.street}, {address.city}, {address.emirate}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {address.country} - {address.postalCode}
                      </Typography>
                    </Box>
                  }
                  sx={{ width: '100%', m: 0 }}
                />
              </CardContent>
            </Card>
          ))}
        </RadioGroup>
      </FormControl>

      <Button
        variant="outlined"
        startIcon={<Add />}
        sx={{ mt: 2 }}
        onClick={() => showError('Add new address feature coming soon')}
      >
        {t('checkout.addNewAddress')}
      </Button>
    </Box>
  )

  const PaymentStep = () => (
    <Box>
      <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
        {t('checkout.paymentMethod')}
      </Typography>

      <FormControl component="fieldset" fullWidth>
        <RadioGroup
          value={paymentMethod}
          onChange={(e) => setPaymentMethod(e.target.value)}
        >
          <Card sx={{ mb: 2 }}>
            <CardContent>
              <FormControlLabel
                value="cod"
                control={<Radio />}
                label={
                  <Box sx={{ ml: 1 }}>
                    <Typography variant="h6" sx={{ fontWeight: 600 }}>
                      {t('checkout.cashOnDelivery')}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Pay with cash when your order is delivered
                    </Typography>
                  </Box>
                }
                sx={{ width: '100%', m: 0 }}
              />
            </CardContent>
          </Card>
        </RadioGroup>
      </FormControl>

      <Alert severity="info" sx={{ mt: 2 }}>
        Currently, we only accept Cash on Delivery. More payment options coming soon!
      </Alert>
    </Box>
  )

  const OrderSummaryStep = () => (
    <Box>
      <Typography variant="h6" sx={{ mb: 3, fontWeight: 600 }}>
        {t('checkout.orderSummary')}
      </Typography>

      {/* Delivery Address */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            {t('checkout.deliveryAddress')}
          </Typography>
          {getSelectedAddress() && (
            <Box>
              <Typography variant="body1" sx={{ fontWeight: 600 }}>
                {getSelectedAddress().name}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {getSelectedAddress().street}, {getSelectedAddress().city}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {getSelectedAddress().emirate}, {getSelectedAddress().country}
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Payment Method */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            {t('checkout.paymentMethod')}
          </Typography>
          <Typography variant="body1">
            {t('checkout.cashOnDelivery')}
          </Typography>
        </CardContent>
      </Card>

      {/* Order Items */}
      <Card>
        <CardContent>
          <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
            Order Items ({items.length})
          </Typography>
          <List disablePadding>
            {items.map((item, index) => (
              <React.Fragment key={item.id}>
                <ListItem sx={{ px: 0 }}>
                  <ListItemAvatar>
                    <Avatar src={item.image} variant="rounded" />
                  </ListItemAvatar>
                  <ListItemText
                    primary={item.name}
                    secondary={`${item.quantity} × AED ${item.price.toFixed(2)}`}
                  />
                  <Typography variant="body1" sx={{ fontWeight: 600 }}>
                    AED {(item.quantity * item.price).toFixed(2)}
                  </Typography>
                </ListItem>
                {index < items.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        </CardContent>
      </Card>
    </Box>
  )

  const getStepContent = (step) => {
    switch (step) {
      case 0:
        return <AddressStep />
      case 1:
        return <PaymentStep />
      case 2:
        return <OrderSummaryStep />
      default:
        return 'Unknown step'
    }
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <Typography variant="h4" component="h1" sx={{ fontWeight: 600, mb: 4 }}>
          {t('checkout.title')}
        </Typography>

        <Grid container spacing={4}>
          {/* Checkout Steps */}
          <Grid item xs={12} md={8}>
            <Paper sx={{ p: 3, mb: 3 }}>
              <Stepper activeStep={activeStep} alternativeLabel={!isMobile}>
                {steps.map((label) => (
                  <Step key={label}>
                    <StepLabel>{label}</StepLabel>
                  </Step>
                ))}
              </Stepper>
            </Paper>

            <Paper sx={{ p: 3 }}>
              {getStepContent(activeStep)}

              <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
                <Button
                  onClick={activeStep === 0 ? () => navigate('/cart') : handleBack}
                  startIcon={<ArrowBack />}
                >
                  {activeStep === 0 ? 'Back to Cart' : 'Back'}
                </Button>

                <Button
                  variant="contained"
                  onClick={handleNext}
                  disabled={loading}
                  endIcon={loading ? <CircularProgress size={20} /> : <ArrowForward />}
                >
                  {activeStep === steps.length - 1 ? t('checkout.placeOrder') : 'Next'}
                </Button>
              </Box>
            </Paper>
          </Grid>

          {/* Order Summary Sidebar */}
          <Grid item xs={12} md={4}>
            <Paper sx={{ p: 3, position: 'sticky', top: 100 }}>
              <Typography variant="h6" sx={{ fontWeight: 600, mb: 3 }}>
                {t('checkout.orderSummary')}
              </Typography>

              <List disablePadding>
                <ListItem sx={{ px: 0 }}>
                  <ListItemText primary="Subtotal" />
                  <Typography variant="body1" sx={{ fontWeight: 600 }}>
                    AED {getSubtotal().toFixed(2)}
                  </Typography>
                </ListItem>

                <ListItem sx={{ px: 0 }}>
                  <ListItemText
                    primary="Delivery Fee"
                    secondary={getSubtotal() > 100 ? "Free delivery on orders over AED 100" : ""}
                  />
                  <Typography variant="body1" sx={{ fontWeight: 600 }}>
                    {getDeliveryFee() === 0 ? 'Free' : `AED ${getDeliveryFee().toFixed(2)}`}
                  </Typography>
                </ListItem>

                <Divider sx={{ my: 2 }} />

                <ListItem sx={{ px: 0 }}>
                  <ListItemText
                    primary={
                      <Typography variant="h6" sx={{ fontWeight: 600 }}>
                        {t('checkout.orderTotal')}
                      </Typography>
                    }
                  />
                  <Typography variant="h6" sx={{ fontWeight: 600, color: 'primary.main' }}>
                    AED {getTotal().toFixed(2)}
                  </Typography>
                </ListItem>
              </List>

              <Alert severity="info" sx={{ mt: 3 }}>
                <Typography variant="body2">
                  Estimated delivery: Tomorrow
                </Typography>
              </Alert>
            </Paper>
          </Grid>
        </Grid>
      </motion.div>
    </Container>
  )
}

export default CheckoutPage
