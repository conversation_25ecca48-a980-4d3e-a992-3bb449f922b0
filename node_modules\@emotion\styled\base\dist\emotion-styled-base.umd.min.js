!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("@emotion/react"),require("react")):"function"==typeof define&&define.amd?define(["@emotion/react","react"],t):(e="undefined"!=typeof globalThis?globalThis:e||self).emotionStyledBase=t(e.emotionReact,e.React)}(this,(function(e,t){"use strict";function r(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var n=r(t);function o(){return o=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e},o.apply(this,arguments)}var i={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1};function a(e){var t=Object.create(null);return function(r){return void 0===t[r]&&(t[r]=e(r)),t[r]}}var s=!1,l=/[A-Z]|^ms/g,c=/_EMO_([^_]+?)_([^]*?)_EMO_/g,d=function(e){return 45===e.charCodeAt(1)},u=function(e){return null!=e&&"boolean"!=typeof e},f=a((function(e){return d(e)?e:e.replace(l,"-$&").toLowerCase()})),p=function(e,t){switch(e){case"animation":case"animationName":if("string"==typeof t)return t.replace(c,(function(e,t,r){return g={name:t,styles:r,next:g},t}))}return 1===i[e]||d(e)||"number"!=typeof t||0===t?t:t+"px"},m="Component selectors can only be used in conjunction with @emotion/babel-plugin, the swc Emotion plugin, or another Emotion-aware compiler transform.";function h(e,t,r){if(null==r)return"";var n=r;if(void 0!==n.__emotion_styles)return n;switch(typeof r){case"boolean":return"";case"object":var o=r;if(1===o.anim)return g={name:o.name,styles:o.styles,next:g},o.name;var i=r;if(void 0!==i.styles){var a=i.next;if(void 0!==a)for(;void 0!==a;)g={name:a.name,styles:a.styles,next:g},a=a.next;return i.styles+";"}return function(e,t,r){var n="";if(Array.isArray(r))for(var o=0;o<r.length;o++)n+=h(e,t,r[o])+";";else for(var i in r){var a=r[i];if("object"!=typeof a){var l=a;null!=t&&void 0!==t[l]?n+=i+"{"+t[l]+"}":u(l)&&(n+=f(i)+":"+p(i,l)+";")}else{if("NO_COMPONENT_SELECTOR"===i&&s)throw new Error(m);if(!Array.isArray(a)||"string"!=typeof a[0]||null!=t&&void 0!==t[a[0]]){var c=h(e,t,a);switch(i){case"animation":case"animationName":n+=f(i)+":"+c+";";break;default:n+=i+"{"+c+"}"}}else for(var d=0;d<a.length;d++)u(a[d])&&(n+=f(i)+":"+p(i,a[d])+";")}}return n}(e,t,r);case"function":if(void 0!==e){var l=g,c=r(e);return g=l,h(e,t,c)}}var d=r;if(null==t)return d;var y=t[d];return void 0!==y?y:d}var g,y=/label:\s*([^\s;{]+)\s*(;|$)/g;var v=!!n.useInsertionEffect&&n.useInsertionEffect||function(e){return e()};var b=function(e,t,r){var n=e.key+"-"+t.name;!1===r&&void 0===e.registered[n]&&(e.registered[n]=t.styles)},x=/^((children|dangerouslySetInnerHTML|key|ref|autoFocus|defaultValue|defaultChecked|innerHTML|suppressContentEditableWarning|suppressHydrationWarning|valueLink|abbr|accept|acceptCharset|accessKey|action|allow|allowUserMedia|allowPaymentRequest|allowFullScreen|allowTransparency|alt|async|autoComplete|autoPlay|capture|cellPadding|cellSpacing|challenge|charSet|checked|cite|classID|className|cols|colSpan|content|contentEditable|contextMenu|controls|controlsList|coords|crossOrigin|data|dateTime|decoding|default|defer|dir|disabled|disablePictureInPicture|disableRemotePlayback|download|draggable|encType|enterKeyHint|fetchpriority|fetchPriority|form|formAction|formEncType|formMethod|formNoValidate|formTarget|frameBorder|headers|height|hidden|high|href|hrefLang|htmlFor|httpEquiv|id|inputMode|integrity|is|keyParams|keyType|kind|label|lang|list|loading|loop|low|marginHeight|marginWidth|max|maxLength|media|mediaGroup|method|min|minLength|multiple|muted|name|nonce|noValidate|open|optimum|pattern|placeholder|playsInline|poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbox|scope|scoped|scrolling|seamless|selected|shape|size|sizes|slot|span|spellCheck|src|srcDoc|srcLang|srcSet|start|step|style|summary|tabIndex|target|title|translate|type|useMap|value|width|wmode|wrap|about|datatype|inlist|prefix|property|resource|typeof|vocab|autoCapitalize|autoCorrect|autoSave|color|incremental|fallback|inert|itemProp|itemScope|itemType|itemID|itemRef|on|option|results|security|unselectable|accentHeight|accumulate|additive|alignmentBaseline|allowReorder|alphabetic|amplitude|arabicForm|ascent|attributeName|attributeType|autoReverse|azimuth|baseFrequency|baselineShift|baseProfile|bbox|begin|bias|by|calcMode|capHeight|clip|clipPathUnits|clipPath|clipRule|colorInterpolation|colorInterpolationFilters|colorProfile|colorRendering|contentScriptType|contentStyleType|cursor|cx|cy|d|decelerate|descent|diffuseConstant|direction|display|divisor|dominantBaseline|dur|dx|dy|edgeMode|elevation|enableBackground|end|exponent|externalResourcesRequired|fill|fillOpacity|fillRule|filter|filterRes|filterUnits|floodColor|floodOpacity|focusable|fontFamily|fontSize|fontSizeAdjust|fontStretch|fontStyle|fontVariant|fontWeight|format|from|fr|fx|fy|g1|g2|glyphName|glyphOrientationHorizontal|glyphOrientationVertical|glyphRef|gradientTransform|gradientUnits|hanging|horizAdvX|horizOriginX|ideographic|imageRendering|in|in2|intercept|k|k1|k2|k3|k4|kernelMatrix|kernelUnitLength|kerning|keyPoints|keySplines|keyTimes|lengthAdjust|letterSpacing|lightingColor|limitingConeAngle|local|markerEnd|markerMid|markerStart|markerHeight|markerUnits|markerWidth|mask|maskContentUnits|maskUnits|mathematical|mode|numOctaves|offset|opacity|operator|order|orient|orientation|origin|overflow|overlinePosition|overlineThickness|panose1|paintOrder|pathLength|patternContentUnits|patternTransform|patternUnits|pointerEvents|points|pointsAtX|pointsAtY|pointsAtZ|preserveAlpha|preserveAspectRatio|primitiveUnits|r|radius|refX|refY|renderingIntent|repeatCount|repeatDur|requiredExtensions|requiredFeatures|restart|result|rotate|rx|ry|scale|seed|shapeRendering|slope|spacing|specularConstant|specularExponent|speed|spreadMethod|startOffset|stdDeviation|stemh|stemv|stitchTiles|stopColor|stopOpacity|strikethroughPosition|strikethroughThickness|string|stroke|strokeDasharray|strokeDashoffset|strokeLinecap|strokeLinejoin|strokeMiterlimit|strokeOpacity|strokeWidth|surfaceScale|systemLanguage|tableValues|targetX|targetY|textAnchor|textDecoration|textRendering|textLength|to|transform|u1|u2|underlinePosition|underlineThickness|unicode|unicodeBidi|unicodeRange|unitsPerEm|vAlphabetic|vHanging|vIdeographic|vMathematical|values|vectorEffect|version|vertAdvY|vertOriginX|vertOriginY|viewBox|viewTarget|visibility|widths|wordSpacing|writingMode|x|xHeight|x1|x2|xChannelSelector|xlinkActuate|xlinkArcrole|xlinkHref|xlinkRole|xlinkShow|xlinkTitle|xlinkType|xmlBase|xmlns|xmlnsXlink|xmlLang|xmlSpace|y|y1|y2|yChannelSelector|z|zoomAndPan|for|class|autofocus)|(([Dd][Aa][Tt][Aa]|[Aa][Rr][Ii][Aa]|x)-.*))$/,k=a((function(e){return x.test(e)||111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)<91})),w=function(e){return"theme"!==e},C=function(e){return"string"==typeof e&&e.charCodeAt(0)>96?k:w},_=function(e,t,r){var n;if(t){var o=t.shouldForwardProp;n=e.__emotion_forwardProp&&o?function(t){return e.__emotion_forwardProp(t)&&o(t)}:o}return"function"!=typeof n&&r&&(n=e.__emotion_forwardProp),n},S=function(e){var t=e.cache,r=e.serialized,n=e.isStringTag;return b(t,r,n),v((function(){return function(e,t,r){b(e,t,r);var n=e.key+"-"+t.name;if(void 0===e.inserted[t.name]){var o=t;do{e.insert(t===o?"."+n:"",o,e.sheet,!0),o=o.next}while(void 0!==o)}}(t,r,n)})),null};return function t(r,i){var a,s,l=r.__emotion_real===r,c=l&&r.__emotion_base||r;void 0!==i&&(a=i.label,s=i.target);var d=_(r,i,l),u=d||C(c),f=!u("as");return function(){var p=arguments,m=l&&void 0!==r.__emotion_styles?r.__emotion_styles.slice(0):[];if(void 0!==a&&m.push("label:"+a+";"),null==p[0]||void 0===p[0].raw)m.push.apply(m,p);else{var v=p[0];m.push(v[0]);for(var b=p.length,x=1;x<b;x++)m.push(p[x],v[x])}var k=e.withEmotionCache((function(t,r,o){var i,a,l,p,v=f&&t.as||c,b="",x=[],k=t;if(null==t.theme){for(var w in k={},t)k[w]=t[w];k.theme=n.useContext(e.ThemeContext)}"string"==typeof t.className?(i=r.registered,a=x,l=t.className,p="",l.split(" ").forEach((function(e){void 0!==i[e]?a.push(i[e]+";"):e&&(p+=e+" ")})),b=p):null!=t.className&&(b=t.className+" ");var _=function(e,t,r){if(1===e.length&&"object"==typeof e[0]&&null!==e[0]&&void 0!==e[0].styles)return e[0];var n=!0,o="";g=void 0;var i=e[0];null==i||void 0===i.raw?(n=!1,o+=h(r,t,i)):o+=i[0];for(var a=1;a<e.length;a++)o+=h(r,t,e[a]),n&&(o+=i[a]);y.lastIndex=0;for(var s,l="";null!==(s=y.exec(o));)l+="-"+s[1];var c=function(e){for(var t,r=0,n=0,o=e.length;o>=4;++n,o-=4)t=1540483477*(65535&(t=255&e.charCodeAt(n)|(255&e.charCodeAt(++n))<<8|(255&e.charCodeAt(++n))<<16|(255&e.charCodeAt(++n))<<24))+(59797*(t>>>16)<<16),r=1540483477*(65535&(t^=t>>>24))+(59797*(t>>>16)<<16)^1540483477*(65535&r)+(59797*(r>>>16)<<16);switch(o){case 3:r^=(255&e.charCodeAt(n+2))<<16;case 2:r^=(255&e.charCodeAt(n+1))<<8;case 1:r=1540483477*(65535&(r^=255&e.charCodeAt(n)))+(59797*(r>>>16)<<16)}return(((r=1540483477*(65535&(r^=r>>>13))+(59797*(r>>>16)<<16))^r>>>15)>>>0).toString(36)}(o)+l;return{name:c,styles:o,next:g}}(m.concat(x),r.registered,k);b+=r.key+"-"+_.name,void 0!==s&&(b+=" "+s);var O=f&&void 0===d?C(v):u,A={};for(var P in t)f&&"as"===P||O(P)&&(A[P]=t[P]);return A.className=b,o&&(A.ref=o),n.createElement(n.Fragment,null,n.createElement(S,{cache:r,serialized:_,isStringTag:"string"==typeof v}),n.createElement(v,A))}));return k.displayName=void 0!==a?a:"Styled("+("string"==typeof c?c:c.displayName||c.name||"Component")+")",k.defaultProps=r.defaultProps,k.__emotion_real=k,k.__emotion_base=c,k.__emotion_styles=m,k.__emotion_forwardProp=d,Object.defineProperty(k,"toString",{value:function(){return"."+s}}),k.withComponent=function(e,r){return t(e,o({},i,r,{shouldForwardProp:_(k,r,!0)})).apply(void 0,m)},k}}}));
//# sourceMappingURL=emotion-styled-base.umd.min.js.map
