!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports,require("react/jsx-dev-runtime"),require("react")):"function"==typeof define&&define.amd?define(["exports","react/jsx-dev-runtime","react"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).emotionReactJSXDevRuntime={},e.<PERSON>ev,e.<PERSON>)}(this,(function(e,t,r){"use strict";function n(e){if(e&&e.__esModule)return e;var t=Object.create(null);return e&&Object.keys(e).forEach((function(r){if("default"!==r){var n=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(t,r,n.get?n:{enumerable:!0,get:function(){return e[r]}})}})),t.default=e,Object.freeze(t)}var a=n(t),s=n(r);var c=function(){function e(e){var t=this;this._insertTag=function(e){var r;r=0===t.tags.length?t.insertionPoint?t.insertionPoint.nextSibling:t.prepend?t.container.firstChild:t.before:t.tags[t.tags.length-1].nextSibling,t.container.insertBefore(e,r),t.tags.push(e)},this.isSpeedy=void 0===e.speedy||e.speedy,this.tags=[],this.ctr=0,this.nonce=e.nonce,this.key=e.key,this.container=e.container,this.prepend=e.prepend,this.insertionPoint=e.insertionPoint,this.before=null}var t=e.prototype;return t.hydrate=function(e){e.forEach(this._insertTag)},t.insert=function(e){this.ctr%(this.isSpeedy?65e3:1)==0&&this._insertTag(function(e){var t=document.createElement("style");return t.setAttribute("data-emotion",e.key),void 0!==e.nonce&&t.setAttribute("nonce",e.nonce),t.appendChild(document.createTextNode("")),t.setAttribute("data-s",""),t}(this));var t=this.tags[this.tags.length-1];if(this.isSpeedy){var r=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]}(t);try{r.insertRule(e,r.cssRules.length)}catch(e){}}else t.appendChild(document.createTextNode(e));this.ctr++},t.flush=function(){this.tags.forEach((function(e){var t;return null==(t=e.parentNode)?void 0:t.removeChild(e)})),this.tags=[],this.ctr=0},e}(),i="-ms-",o="-moz-",u="-webkit-",l="comm",f="rule",d="decl",h="@keyframes",p=Math.abs,v=String.fromCharCode,m=Object.assign;function g(e){return e.trim()}function y(e,t,r){return e.replace(t,r)}function b(e,t){return e.indexOf(t)}function w(e,t){return 0|e.charCodeAt(t)}function x(e,t,r){return e.slice(t,r)}function k(e){return e.length}function C(e){return e.length}function $(e,t){return t.push(e),e}var E=1,O=1,A=0,S=0,_=0,j="";function N(e,t,r,n,a,s,c){return{value:e,root:t,parent:r,type:n,props:a,children:s,line:E,column:O,length:c,return:""}}function P(e,t){return m(N("",null,null,"",null,null,0),e,{length:-e.length},t)}function R(){return _=S>0?w(j,--S):0,O--,10===_&&(O=1,E--),_}function T(){return _=S<A?w(j,S++):0,O++,10===_&&(O=1,E++),_}function M(){return w(j,S)}function z(){return S}function D(e,t){return x(j,e,t)}function I(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function G(e){return E=O=1,A=k(j=e),S=0,[]}function F(e){return j="",e}function L(e){return g(D(S-1,V(91===e?e+2:40===e?e+1:e)))}function W(e){for(;(_=M())&&_<33;)T();return I(e)>2||I(_)>3?"":" "}function q(e,t){for(;--t&&T()&&!(_<48||_>102||_>57&&_<65||_>70&&_<97););return D(e,z()+(t<6&&32==M()&&32==T()))}function V(e){for(;T();)switch(_){case e:return S;case 34:case 39:34!==e&&39!==e&&V(_);break;case 40:41===e&&V(e);break;case 92:T()}return S}function H(e,t){for(;T()&&e+_!==57&&(e+_!==84||47!==M()););return"/*"+D(t,S-1)+"*"+v(47===e?e:T())}function J(e){for(;!I(M());)T();return D(e,S)}function X(e){return F(B("",null,null,null,[""],e=G(e),0,[0],e))}function B(e,t,r,n,a,s,c,i,o){for(var u=0,l=0,f=c,d=0,h=0,p=0,m=1,g=1,x=1,C=0,E="",O=a,A=s,S=n,_=E;g;)switch(p=C,C=T()){case 40:if(108!=p&&58==w(_,f-1)){-1!=b(_+=y(L(C),"&","&\f"),"&\f")&&(x=-1);break}case 34:case 39:case 91:_+=L(C);break;case 9:case 10:case 13:case 32:_+=W(p);break;case 92:_+=q(z()-1,7);continue;case 47:switch(M()){case 42:case 47:$(Y(H(T(),z()),t,r),o);break;default:_+="/"}break;case 123*m:i[u++]=k(_)*x;case 125*m:case 59:case 0:switch(C){case 0:case 125:g=0;case 59+l:-1==x&&(_=y(_,/\f/g,"")),h>0&&k(_)-f&&$(h>32?Z(_+";",n,r,f-1):Z(y(_," ","")+";",n,r,f-2),o);break;case 59:_+=";";default:if($(S=U(_,t,r,u,l,a,i,E,O=[],A=[],f),s),123===C)if(0===l)B(_,t,S,S,O,s,f,i,A);else switch(99===d&&110===w(_,3)?100:d){case 100:case 108:case 109:case 115:B(e,S,S,n&&$(U(e,S,S,0,0,a,i,E,a,O=[],f),A),a,A,f,i,n?O:A);break;default:B(_,S,S,S,[""],A,0,i,A)}}u=l=h=0,m=x=1,E=_="",f=c;break;case 58:f=1+k(_),h=p;default:if(m<1)if(123==C)--m;else if(125==C&&0==m++&&125==R())continue;switch(_+=v(C),C*m){case 38:x=l>0?1:(_+="\f",-1);break;case 44:i[u++]=(k(_)-1)*x,x=1;break;case 64:45===M()&&(_+=L(T())),d=M(),l=f=k(E=_+=J(z())),C++;break;case 45:45===p&&2==k(_)&&(m=0)}}return s}function U(e,t,r,n,a,s,c,i,o,u,l){for(var d=a-1,h=0===a?s:[""],v=C(h),m=0,b=0,w=0;m<n;++m)for(var k=0,$=x(e,d+1,d=p(b=c[m])),E=e;k<v;++k)(E=g(b>0?h[k]+" "+$:y($,/&\f/g,h[k])))&&(o[w++]=E);return N(e,t,r,0===a?f:i,o,u,l)}function Y(e,t,r){return N(e,t,r,l,v(_),x(e,2,-2),0)}function Z(e,t,r,n){return N(e,t,r,d,x(e,0,n),x(e,n+1,-1),n)}function K(e,t){for(var r="",n=C(e),a=0;a<n;a++)r+=t(e[a],a,e,t)||"";return r}function Q(e,t,r,n){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case d:return e.return=e.return||e.value;case l:return"";case h:return e.return=e.value+"{"+K(e.children,n)+"}";case f:e.value=e.props.join(",")}return k(r=K(e.children,n))?e.return=e.value+"{"+r+"}":""}function ee(e){var t=Object.create(null);return function(r){return void 0===t[r]&&(t[r]=e(r)),t[r]}}var te=function(e,t,r){for(var n=0,a=0;n=a,a=M(),38===n&&12===a&&(t[r]=1),!I(a);)T();return D(e,S)},re=function(e,t){return F(function(e,t){var r=-1,n=44;do{switch(I(n)){case 0:38===n&&12===M()&&(t[r]=1),e[r]+=te(S-1,t,r);break;case 2:e[r]+=L(n);break;case 4:if(44===n){e[++r]=58===M()?"&\f":"",t[r]=e[r].length;break}default:e[r]+=v(n)}}while(n=T());return e}(G(e),t))},ne=new WeakMap,ae=function(e){if("rule"===e.type&&e.parent&&!(e.length<1)){for(var t=e.value,r=e.parent,n=e.column===r.column&&e.line===r.line;"rule"!==r.type;)if(!(r=r.parent))return;if((1!==e.props.length||58===t.charCodeAt(0)||ne.get(r))&&!n){ne.set(e,!0);for(var a=[],s=re(t,a),c=r.props,i=0,o=0;i<s.length;i++)for(var u=0;u<c.length;u++,o++)e.props[o]=a[i]?s[i].replace(/&\f/g,c[u]):c[u]+" "+s[i]}}},se=function(e){if("decl"===e.type){var t=e.value;108===t.charCodeAt(0)&&98===t.charCodeAt(2)&&(e.return="",e.value="")}};function ce(e,t){switch(function(e,t){return 45^w(e,0)?(((t<<2^w(e,0))<<2^w(e,1))<<2^w(e,2))<<2^w(e,3):0}(e,t)){case 5103:return u+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return u+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return u+e+o+e+i+e+e;case 6828:case 4268:return u+e+i+e+e;case 6165:return u+e+i+"flex-"+e+e;case 5187:return u+e+y(e,/(\w+).+(:[^]+)/,u+"box-$1$2"+i+"flex-$1$2")+e;case 5443:return u+e+i+"flex-item-"+y(e,/flex-|-self/,"")+e;case 4675:return u+e+i+"flex-line-pack"+y(e,/align-content|flex-|-self/,"")+e;case 5548:return u+e+i+y(e,"shrink","negative")+e;case 5292:return u+e+i+y(e,"basis","preferred-size")+e;case 6060:return u+"box-"+y(e,"-grow","")+u+e+i+y(e,"grow","positive")+e;case 4554:return u+y(e,/([^-])(transform)/g,"$1"+u+"$2")+e;case 6187:return y(y(y(e,/(zoom-|grab)/,u+"$1"),/(image-set)/,u+"$1"),e,"")+e;case 5495:case 3959:return y(e,/(image-set\([^]*)/,u+"$1$`$1");case 4968:return y(y(e,/(.+:)(flex-)?(.*)/,u+"box-pack:$3"+i+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+u+e+e;case 4095:case 3583:case 4068:case 2532:return y(e,/(.+)-inline(.+)/,u+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(k(e)-1-t>6)switch(w(e,t+1)){case 109:if(45!==w(e,t+4))break;case 102:return y(e,/(.+:)(.+)-([^]+)/,"$1"+u+"$2-$3$1"+o+(108==w(e,t+3)?"$3":"$2-$3"))+e;case 115:return~b(e,"stretch")?ce(y(e,"stretch","fill-available"),t)+e:e}break;case 4949:if(115!==w(e,t+1))break;case 6444:switch(w(e,k(e)-3-(~b(e,"!important")&&10))){case 107:return y(e,":",":"+u)+e;case 101:return y(e,/(.+:)([^;!]+)(;|!.+)?/,"$1"+u+(45===w(e,14)?"inline-":"")+"box$3$1"+u+"$2$3$1"+i+"$2box$3")+e}break;case 5936:switch(w(e,t+11)){case 114:return u+e+i+y(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return u+e+i+y(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return u+e+i+y(e,/[svh]\w+-[tblr]{2}/,"lr")+e}return u+e+i+e+e}return e}var ie=[function(e,t,r,n){if(e.length>-1&&!e.return)switch(e.type){case d:e.return=ce(e.value,e.length);break;case h:return K([P(e,{value:y(e.value,"@","@"+u)})],n);case f:if(e.length)return function(e,t){return e.map(t).join("")}(e.props,(function(t){switch(function(e,t){return(e=t.exec(e))?e[0]:e}(t,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return K([P(e,{props:[y(t,/:(read-\w+)/,":"+o+"$1")]})],n);case"::placeholder":return K([P(e,{props:[y(t,/:(plac\w+)/,":"+u+"input-$1")]}),P(e,{props:[y(t,/:(plac\w+)/,":"+o+"$1")]}),P(e,{props:[y(t,/:(plac\w+)/,i+"input-$1")]})],n)}return""}))}}],oe=function(e){var t=e.key;if("css"===t){var r=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(r,(function(e){-1!==e.getAttribute("data-emotion").indexOf(" ")&&(document.head.appendChild(e),e.setAttribute("data-s",""))}))}var n,a,s=e.stylisPlugins||ie,i={},o=[];n=e.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+t+' "]'),(function(e){for(var t=e.getAttribute("data-emotion").split(" "),r=1;r<t.length;r++)i[t[r]]=!0;o.push(e)}));var u,l,f=[Q,(l=function(e){u.insert(e)},function(e){e.root||(e=e.return)&&l(e)})],d=function(e){var t=C(e);return function(r,n,a,s){for(var c="",i=0;i<t;i++)c+=e[i](r,n,a,s)||"";return c}}([ae,se].concat(s,f));a=function(e,t,r,n){u=r,K(X(e?e+"{"+t.styles+"}":t.styles),d),n&&(h.inserted[t.name]=!0)};var h={key:t,sheet:new c({key:t,container:n,nonce:e.nonce,speedy:e.speedy,prepend:e.prepend,insertionPoint:e.insertionPoint}),nonce:e.nonce,inserted:i,registered:{},insert:a};return h.sheet.hydrate(o),h},ue=s.createContext("undefined"!=typeof HTMLElement?oe({key:"css"}):null);ue.Provider;var le=function(e){return r.forwardRef((function(t,n){var a=r.useContext(ue);return e(t,a,n)}))},fe=s.createContext({});var de=function(e,t,r){var n=e.key+"-"+t.name;!1===r&&void 0===e.registered[n]&&(e.registered[n]=t.styles)},he={}.hasOwnProperty;var pe={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1},ve=!1,me=/[A-Z]|^ms/g,ge=/_EMO_([^_]+?)_([^]*?)_EMO_/g,ye=function(e){return 45===e.charCodeAt(1)},be=function(e){return null!=e&&"boolean"!=typeof e},we=ee((function(e){return ye(e)?e:e.replace(me,"-$&").toLowerCase()})),xe=function(e,t){switch(e){case"animation":case"animationName":if("string"==typeof t)return t.replace(ge,(function(e,t,r){return $e={name:t,styles:r,next:$e},t}))}return 1===pe[e]||ye(e)||"number"!=typeof t||0===t?t:t+"px"},ke="Component selectors can only be used in conjunction with @emotion/babel-plugin, the swc Emotion plugin, or another Emotion-aware compiler transform.";function Ce(e,t,r){if(null==r)return"";var n=r;if(void 0!==n.__emotion_styles)return n;switch(typeof r){case"boolean":return"";case"object":var a=r;if(1===a.anim)return $e={name:a.name,styles:a.styles,next:$e},a.name;var s=r;if(void 0!==s.styles){var c=s.next;if(void 0!==c)for(;void 0!==c;)$e={name:c.name,styles:c.styles,next:$e},c=c.next;return s.styles+";"}return function(e,t,r){var n="";if(Array.isArray(r))for(var a=0;a<r.length;a++)n+=Ce(e,t,r[a])+";";else for(var s in r){var c=r[s];if("object"!=typeof c){var i=c;null!=t&&void 0!==t[i]?n+=s+"{"+t[i]+"}":be(i)&&(n+=we(s)+":"+xe(s,i)+";")}else{if("NO_COMPONENT_SELECTOR"===s&&ve)throw new Error(ke);if(!Array.isArray(c)||"string"!=typeof c[0]||null!=t&&void 0!==t[c[0]]){var o=Ce(e,t,c);switch(s){case"animation":case"animationName":n+=we(s)+":"+o+";";break;default:n+=s+"{"+o+"}"}}else for(var u=0;u<c.length;u++)be(c[u])&&(n+=we(s)+":"+xe(s,c[u])+";")}}return n}(e,t,r);case"function":if(void 0!==e){var i=$e,o=r(e);return $e=i,Ce(e,t,o)}}var u=r;if(null==t)return u;var l=t[u];return void 0!==l?l:u}var $e,Ee=/label:\s*([^\s;{]+)\s*(;|$)/g;var Oe=!!s.useInsertionEffect&&s.useInsertionEffect||function(e){return e()},Ae="__EMOTION_TYPE_PLEASE_DO_NOT_USE__",Se=function(e){var t=e.cache,r=e.serialized,n=e.isStringTag;return de(t,r,n),Oe((function(){return function(e,t,r){de(e,t,r);var n=e.key+"-"+t.name;if(void 0===e.inserted[t.name]){var a=t;do{e.insert(t===a?"."+n:"",a,e.sheet,!0),a=a.next}while(void 0!==a)}}(t,r,n)})),null},_e=le((function(e,t,r){var n=e.css;"string"==typeof n&&void 0!==t.registered[n]&&(n=t.registered[n]);var a=e[Ae],c=[n],i="";"string"==typeof e.className?i=function(e,t,r){var n="";return r.split(" ").forEach((function(r){void 0!==e[r]?t.push(e[r]+";"):r&&(n+=r+" ")})),n}(t.registered,c,e.className):null!=e.className&&(i=e.className+" ");var o=function(e,t,r){if(1===e.length&&"object"==typeof e[0]&&null!==e[0]&&void 0!==e[0].styles)return e[0];var n=!0,a="";$e=void 0;var s=e[0];null==s||void 0===s.raw?(n=!1,a+=Ce(r,t,s)):a+=s[0];for(var c=1;c<e.length;c++)a+=Ce(r,t,e[c]),n&&(a+=s[c]);Ee.lastIndex=0;for(var i,o="";null!==(i=Ee.exec(a));)o+="-"+i[1];return{name:function(e){for(var t,r=0,n=0,a=e.length;a>=4;++n,a-=4)t=1540483477*(65535&(t=255&e.charCodeAt(n)|(255&e.charCodeAt(++n))<<8|(255&e.charCodeAt(++n))<<16|(255&e.charCodeAt(++n))<<24))+(59797*(t>>>16)<<16),r=1540483477*(65535&(t^=t>>>24))+(59797*(t>>>16)<<16)^1540483477*(65535&r)+(59797*(r>>>16)<<16);switch(a){case 3:r^=(255&e.charCodeAt(n+2))<<16;case 2:r^=(255&e.charCodeAt(n+1))<<8;case 1:r=1540483477*(65535&(r^=255&e.charCodeAt(n)))+(59797*(r>>>16)<<16)}return(((r=1540483477*(65535&(r^=r>>>13))+(59797*(r>>>16)<<16))^r>>>15)>>>0).toString(36)}(a)+o,styles:a,next:$e}}(c,void 0,s.useContext(fe));i+=t.key+"-"+o.name;var u={};for(var l in e)he.call(e,l)&&"css"!==l&&l!==Ae&&(u[l]=e[l]);return u.className=i,r&&(u.ref=r),s.createElement(s.Fragment,null,s.createElement(Se,{cache:t,serialized:o,isStringTag:"string"==typeof a}),s.createElement(a,u))})),je=_e,Ne=a.Fragment;e.Fragment=Ne,e.jsxDEV=function(e,t,r,n,s,c){return he.call(t,"css")?a.jsxDEV(je,function(e,t){var r={};for(var n in t)he.call(t,n)&&(r[n]=t[n]);return r[Ae]=e,r}(e,t),r,n,s,c):a.jsxDEV(e,t,r,n,s,c)},Object.defineProperty(e,"__esModule",{value:!0})}));
//# sourceMappingURL=emotion-react-jsx-dev-runtime.umd.min.js.map
